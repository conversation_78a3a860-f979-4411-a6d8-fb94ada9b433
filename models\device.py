"""
Local Device Model for Heterogeneous Multi-Edge Server DNN Inference Optimization
Implements battery-aware device with frequency control and energy management
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple
from config import config

class LocalDevice:
    """
    Local device model with battery management and frequency control
    Based on NVIDIA Jetson Xavier specifications from technical document
    """
    
    def __init__(self, device_id: int, initial_battery_ratio: float = 1.0,
                 position: tuple = None):
        """
        Initialize local device

        Args:
            device_id: Unique device identifier
            initial_battery_ratio: Initial battery level as ratio of max capacity (0-1)
            position: Physical position (x, y) in meters, if None will be randomly assigned
        """
        self.device_id = device_id
        self.config = config.device

        # Physical position
        if position is not None:
            self.x, self.y = position
        else:
            self.x = np.random.uniform(0, config.physical.area_width)
            self.y = np.random.uniform(0, config.physical.area_height)
        
        # Battery state
        self.max_battery = self.config.max_battery
        self.mini_battery = self.config.min_battery  # mAh
        # self.initial_battery = np.random.uniform(self.config.min_battery, self.config.max_battery)
        self.initial_battery = self.config.max_battery # mAh
        self.current_battery = self.initial_battery  # mAh
        self.min_battery = self.config.min_battery_ratio * self.max_battery  # mAh
        self.voltage = self.config.voltage  # V
        
        # Frequency state
        self.current_frequency = np.random.uniform(self.config.f_min, self.config.f_max) # Hz
        # self.current_frequency = self.config.f_min # Hz
        self.f_min = self.config.f_min  # Hz
        self.f_max = self.config.f_max  # Hz
        
        # Computing capability
        self.g_device = self.config.g_device  # FLOPS per cycle
        
        # Energy parameters
        self.kappa = self.config.kappa  # W/GHz^3
        self.p_static = np.random.uniform(
            self.config.p_static_min, 
            self.config.p_static_max
        )  # W
        
        # Communication parameters
        self.p_tx = np.random.uniform(
            self.config.p_tx_min,
            self.config.p_tx_max
        )  # W
        
        # State tracking
        self.energy_history = []
        self.frequency_history = []
        self.battery_history = []
        
    def get_state(self) -> np.ndarray:
        """
        Get current device state for RL agent

        Returns:
            State vector: [battery_ratio, frequency_ratio, x_normalized, y_normalized]
        """
        battery_ratio = self.current_battery / self.initial_battery
        frequency_ratio = (self.current_frequency - self.f_min) / (self.f_max - self.f_min)
        x_normalized = self.x / config.physical.area_width
        y_normalized = self.y / config.physical.area_height

        return np.array([battery_ratio, frequency_ratio, x_normalized, y_normalized], dtype=np.float32)
    
    def set_frequency(self, frequency: float) -> bool:
        """
        Set device GPU frequency with constraints
        
        Args:
            frequency: Target frequency in Hz
            
        Returns:
            True if frequency was set successfully, False otherwise
        """
        if self.f_min <= frequency <= self.f_max:
            self.current_frequency = frequency
            return True
        return False
    
    def calculate_computation_energy(self, flops: float, duration: float) -> float:
        """
        Calculate energy consumption for computation
        
        Args:
            flops: Number of floating point operations
            duration: Computation duration in seconds
            
        Returns:
            Energy consumption in Joules
        """
        # E_comp = κ * f^3 * T_local (where f is in GHz, T_local in seconds)
        frequency_ghz = self.current_frequency / 1e9
        computation_energy = self.kappa * (frequency_ghz ** 3) * duration
        
        return computation_energy
    
    def calculate_communication_energy(self, data_size_bits: float, transmission_rate: float) -> float:
        """
        Calculate energy consumption for communication
        
        Args:
            data_size_bits: Data size in bits
            transmission_rate: Transmission rate in bps
            
        Returns:
            Energy consumption in Joules
        """
        # E_comm = P_tx * D_tx / R
        transmission_time = data_size_bits / transmission_rate
        communication_energy = self.p_tx * transmission_time
        
        return communication_energy
    
    def calculate_static_energy(self, total_time: float) -> float:
        """
        Calculate static energy consumption
        
        Args:
            total_time: Total time duration in seconds
            
        Returns:
            Static energy consumption in Joules
        """
        # E_idle = P_static * T_total
        return self.p_static * total_time
    
    def calculate_total_energy(self, flops: float, data_size_bits: float, 
                             transmission_rate: float, total_time: float) -> float:
        """
        Calculate total energy consumption
        
        Args:
            flops: Number of floating point operations
            data_size_bits: Data size for transmission in bits
            transmission_rate: Transmission rate in bps
            total_time: Total time duration in seconds
            
        Returns:
            Total energy consumption in Joules
        """
        computation_time = flops / (self.current_frequency * self.g_device)
        
        comp_energy = self.calculate_computation_energy(flops, computation_time)
        comm_energy = self.calculate_communication_energy(data_size_bits, transmission_rate)
        static_energy = self.calculate_static_energy(total_time)
        
        total_energy = comp_energy + comm_energy + static_energy
        return total_energy
    
    def consume_energy(self, energy_joules: float) -> bool:
        """
        Consume energy from battery
        
        Args:
            energy_joules: Energy to consume in Joules
            
        Returns:
            True if energy was consumed successfully, False if battery depleted
        """
        # Ensure energy consumption is non-negative
        if energy_joules < 0:
            energy_joules = 0.0  # Treat negative energy as zero consumption
        
        # Convert Joules to mAh: Energy(J) / Voltage(V) / 3.6 = mAh
        energy_mah = energy_joules / (self.voltage * 3.6)
        
        if self.current_battery - energy_mah >= self.min_battery:
            self.current_battery -= energy_mah
            self.energy_history.append(energy_joules)
            return True
        else:
            # Battery depleted
            self.current_battery = max(0, self.current_battery - energy_mah)
            self.energy_history.append(energy_joules)
            return False
    
    def is_depleted(self) -> bool:
        """Check if battery is depleted below minimum threshold"""
        return self.current_battery <= self.min_battery
    
    def get_battery_ratio(self) -> float:
        """Get current battery level as ratio of maximum capacity"""
        return self.current_battery / self.initial_battery
    
    def get_adaptive_weight(self) -> float:
        """
        Calculate adaptive weight based on battery level
        α_m = 1 / (1 + exp(-β(B_m/B_max - θ)))
        
        Returns:
            Adaptive weight (0-1) for energy-delay trade-off
        """
        battery_ratio = self.get_battery_ratio()
        beta = config.optimization.beta
        theta = config.optimization.theta
        
        weight = 1.0 / (1.0 + np.exp(-beta * (battery_ratio - theta)))
        return weight
    
    def update_history(self):
        """Update state history for tracking"""
        self.frequency_history.append(self.current_frequency)
        self.battery_history.append(self.current_battery)
    
    def reset(self, initial_battery_ratio: float = 1.0, position: tuple = None):
        """
        Reset device to initial state

        Args:
            initial_battery_ratio: Initial battery level ratio
            position: New physical position (x, y), if None will be randomly assigned
        """
        # Reset physical position
        if position is not None:
            self.x, self.y = position
        else:
            self.x = np.random.uniform(0, config.physical.area_width)
            self.y = np.random.uniform(0, config.physical.area_height)

        # Reset initial battery and current battery together
        self.initial_battery = np.random.uniform(self.config.min_battery, self.config.max_battery)
        self.current_battery = self.initial_battery  # Start with full battery
        self.current_frequency = np.random.uniform(self.config.f_min, self.config.f_max)
        self.energy_history.clear()
        self.frequency_history.clear()
        self.battery_history.clear()
    
    def get_position(self) -> Tuple[float, float]:
        """Get device physical position"""
        return (self.x, self.y)

    def calculate_distance_to(self, target_x: float, target_y: float) -> float:
        """Calculate Euclidean distance to target position"""
        return np.sqrt((self.x - target_x)**2 + (self.y - target_y)**2)

    def get_info(self) -> Dict[str, Any]:
        """Get comprehensive device information"""
        return {
            'device_id': self.device_id,
            'position': (self.x, self.y),
            'battery_mah': self.current_battery,
            'battery_ratio': self.get_battery_ratio(),
            'frequency_hz': self.current_frequency,
            'frequency_ghz': self.current_frequency / 1e9,
            'adaptive_weight': self.get_adaptive_weight(),
            'is_depleted': self.is_depleted(),
            'static_power_w': self.p_static,
            'tx_power_w': self.p_tx,
            'energy_coefficient': self.kappa
        }
    
    def __str__(self) -> str:
        """String representation of device state"""
        info = self.get_info()
        return (f"Device {info['device_id']}: "
                f"Battery={info['battery_ratio']:.2f}, "
                f"Freq={info['frequency_ghz']:.2f}GHz, "
                f"Weight={info['adaptive_weight']:.3f}")
