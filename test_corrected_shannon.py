#!/usr/bin/env python3
"""
测试修正后的香农公式实现 (使用正确的p_tx范围)
Test Corrected Shannon Formula Implementation (with correct p_tx range)
"""

import numpy as np
from config import config
from models import LocalDevice
from environment import EdgeComputingEnvironment

def test_corrected_shannon_formula():
    """测试修正后的香农公式实现"""
    print("="*80)
    print("修正后的香农公式测试: Rn = bn × log2(1 + (pn × hn) / (bn × N0))")
    print("其中 pn = p_tx (设备传输功率)")
    print("="*80)
    
    print("\n📡 1. 修正后的参数配置")
    print("-" * 50)
    print(f"带宽范围: {config.network.bandwidth_min/1e6:.1f} - {config.network.bandwidth_max/1e6:.1f} MHz")
    print(f"设备传输功率范围: {config.device.p_tx_min:.3f} - {config.device.p_tx_max:.3f} W")
    print(f"噪声功率谱密度: {config.network.noise_psd_dbm_hz} dBm/Hz")
    print(f"信道增益常数: {config.physical.channel_gain_constant:.1e}")
    print(f"路径损耗指数: {config.physical.path_loss_exponent}")
    print(f"衰落标准差: {config.physical.fading_std_db} dB")
    print(f"目标速率范围: {config.network.rate_min/1e6:.0f} - {config.network.rate_max/1e6:.0f} Mbps")
    
    print("\n🔧 2. 手动计算验证")
    print("-" * 50)
    
    # 创建设备获取实际p_tx值
    device = LocalDevice(device_id=0)
    
    # 手动计算示例
    distance = 100  # 100米
    bn = 20e6  # 20 MHz
    pn = device.p_tx  # 设备传输功率
    
    print(f"测试参数:")
    print(f"• 距离: {distance} m")
    print(f"• 带宽: {bn/1e6:.1f} MHz")
    print(f"• 设备传输功率: {pn:.3f} W ({10*np.log10(pn*1000):.1f} dBm)")
    
    # 信道增益计算
    hn = config.physical.channel_gain_constant / (distance ** config.physical.path_loss_exponent)
    print(f"• 信道增益: hn = {config.physical.channel_gain_constant:.1e} / {distance}^{config.physical.path_loss_exponent} = {hn:.2e}")
    
    # 噪声功率计算
    N0_dbm_hz = config.network.noise_psd_dbm_hz
    N0_watts_hz = 10 ** ((N0_dbm_hz - 30) / 10)
    print(f"• 噪声功率谱密度: {N0_dbm_hz} dBm/Hz = {N0_watts_hz:.2e} W/Hz")
    
    # SNR计算
    snr_linear = (pn * hn) / (bn * N0_watts_hz)
    snr_db = 10 * np.log10(snr_linear)
    print(f"• SNR = (pn × hn) / (bn × N0)")
    print(f"  SNR = ({pn:.3f} × {hn:.2e}) / ({bn/1e6:.1f}M × {N0_watts_hz:.2e})")
    print(f"  SNR = {snr_linear:.2e} = {snr_db:.2f} dB")
    
    # 容量计算
    capacity_bps = bn * np.log2(1 + snr_linear)
    capacity_mbps = capacity_bps / 1e6
    print(f"• 容量 = bn × log2(1 + SNR)")
    print(f"  容量 = {bn/1e6:.1f}M × log2(1 + {snr_linear:.2e})")
    print(f"  容量 = {capacity_mbps:.2f} Mbps")

def test_device_power_variation():
    """测试不同设备功率下的容量变化"""
    print("\n⚡ 3. 设备功率变化测试")
    print("-" * 50)
    
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    # 测试不同功率值
    power_values = np.linspace(config.device.p_tx_min, config.device.p_tx_max, 10)
    distance_fixed = 200  # 固定距离200m
    
    print("功率(W) | 功率(dBm) | 平均容量(Mbps) | 容量范围(Mbps)")
    print("-" * 65)
    
    for power in power_values:
        capacities = []
        for _ in range(50):  # 每个功率值测试50次
            capacity = env._calculate_shannon_capacity(0, 0, distance_fixed, 0, power)
            if capacity > 0:
                capacities.append(capacity / 1e6)
        
        if capacities:
            avg_capacity = np.mean(capacities)
            min_capacity = np.min(capacities)
            max_capacity = np.max(capacities)
            power_dbm = 10 * np.log10(power * 1000)  # 转换为dBm
            
            print(f" {power:.3f}  |   {power_dbm:5.1f}   |     {avg_capacity:7.1f}      |  {min_capacity:5.1f}-{max_capacity:5.1f}")
        else:
            print(f" {power:.3f}  |   {power_dbm:5.1f}   |      无通信       |     无通信")

def test_rate_distribution():
    """测试速率分布"""
    print("\n📊 4. 速率分布测试")
    print("-" * 50)
    
    env = EdgeComputingEnvironment(num_devices=4, num_servers=4, random_seed=42)
    
    all_rates = []
    print("正在收集速率样本...")
    
    for _ in range(500):  # 收集更多样本
        env._update_network_conditions()
        for condition in env.network_conditions:
            for rate in condition['server_rates']:
                if rate > 0:
                    all_rates.append(rate / 1e6)  # 转换为Mbps
    
    if all_rates:
        rates_array = np.array(all_rates)
        
        print(f"样本数: {len(all_rates)}")
        print(f"速率范围: {np.min(rates_array):.1f} - {np.max(rates_array):.1f} Mbps")
        print(f"平均速率: {np.mean(rates_array):.1f} Mbps")
        print(f"中位数: {np.median(rates_array):.1f} Mbps")
        print(f"标准差: {np.std(rates_array):.1f} Mbps")
        
        # 目标范围分析
        in_target_range = np.sum((rates_array >= 52) & (rates_array <= 108))
        below_target = np.sum(rates_array < 52)
        above_target = np.sum(rates_array > 108)
        
        print(f"\n🎯 目标范围 [52, 108] Mbps 分析:")
        print(f"• 范围内: {in_target_range}/{len(all_rates)} ({in_target_range/len(all_rates)*100:.1f}%)")
        print(f"• 低于范围: {below_target}/{len(all_rates)} ({below_target/len(all_rates)*100:.1f}%)")
        print(f"• 高于范围: {above_target}/{len(all_rates)} ({above_target/len(all_rates)*100:.1f}%)")
        
        # 分位数分析
        percentiles = [10, 25, 50, 75, 90]
        print(f"\n📈 分位数分析:")
        for p in percentiles:
            value = np.percentile(rates_array, p)
            print(f"• {p:2d}%分位数: {value:.1f} Mbps")
        
        return rates_array
    else:
        print("❌ 未收集到有效速率样本")
        return None

def test_distance_effect():
    """测试距离对容量的影响"""
    print("\n📏 5. 距离影响测试")
    print("-" * 50)
    
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    device = env.devices[0]  # 获取设备以使用其p_tx
    
    distances = [10, 50, 100, 200, 300, 500, 800, 1000, 1200, 1500]
    
    print("距离(m) | 平均容量(Mbps) | 速率范围(Mbps) | 样本数")
    print("-" * 60)
    
    for distance in distances:
        rates = []
        for _ in range(100):  # 每个距离100个样本
            rate = env._calculate_shannon_capacity(0, 0, distance, 0, device.p_tx)
            if rate > 0:
                rates.append(rate / 1e6)
        
        if rates:
            avg_rate = np.mean(rates)
            min_rate = np.min(rates)
            max_rate = np.max(rates)
            
            print(f"{distance:6d}  |     {avg_rate:7.1f}      |  {min_rate:5.1f}-{max_rate:5.1f}   |   {len(rates):3d}")
        else:
            print(f"{distance:6d}  |      无通信       |     无通信     |    0")

def test_system_integration():
    """测试系统集成"""
    print("\n🔗 6. 系统集成测试")
    print("-" * 50)
    
    try:
        env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
        
        # 测试环境重置
        states = env.reset()
        print("✅ 环境重置成功")
        
        # 检查设备传输功率
        for i, device in enumerate(env.devices):
            print(f"   设备{i} p_tx: {device.p_tx:.3f} W ({10*np.log10(device.p_tx*1000):.1f} dBm)")
        
        # 测试动作执行
        actions = [np.random.uniform(-1, 1, env.action_dim) for _ in range(env.num_devices)]
        next_states, rewards, dones, info = env.step(actions)
        
        print("✅ 动作执行成功")
        print(f"   奖励: {[f'{r:.3f}' for r in rewards]}")
        
        # 检查网络条件
        for i, condition in enumerate(env.network_conditions):
            rates = condition['server_rates']
            valid_rates = [r/1e6 for r in rates if r > 0]
            if valid_rates:
                print(f"   设备{i}速率: {[f'{r:.1f}Mbps' for r in valid_rates]}")
        
        print("✅ 系统集成正常")
        return True
        
    except Exception as e:
        print(f"❌ 系统集成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("修正后的香农公式实现测试")
    print("pn = p_tx (设备传输功率: 0.093-0.115 W)")
    print("=" * 80)
    
    test_corrected_shannon_formula()
    test_device_power_variation()
    rates_data = test_rate_distribution()
    test_distance_effect()
    integration_success = test_system_integration()
    
    print("\n" + "="*80)
    print("🎉 修正后的香农公式测试完成！")
    print("="*80)
    
    print("✅ 修正内容:")
    print("• pn 现在正确使用设备传输功率 p_tx (0.093-0.115 W)")
    print("• 调整了信道增益常数以适应较低的传输功率")
    print("• 保持目标速率范围 [52, 108] Mbps")
    
    if rates_data is not None:
        compliance = np.sum((rates_data >= 52) & (rates_data <= 108)) / len(rates_data) * 100
        print(f"• 目标范围符合性: {compliance:.1f}%")
        print(f"• 平均速率: {np.mean(rates_data):.1f} Mbps")
    
    if integration_success:
        print("• 系统集成测试通过")
    
    print("\n🚀 香农公式现在使用正确的设备传输功率！")

if __name__ == "__main__":
    main()
