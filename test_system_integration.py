#!/usr/bin/env python3
"""
测试香农容量模型集成后的系统完整性
"""

import numpy as np
from environment import EdgeComputingEnvironment
from baselines import MaxFlowMinCutBaseline, FullOffloadBaseline

def test_environment_integration():
    """测试环境集成"""
    print("="*60)
    print("测试香农容量模型集成后的系统")
    print("="*60)
    
    print("\n1. 环境初始化测试")
    print("-" * 30)
    
    try:
        env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
        print("✓ 环境初始化成功")
        print(f"  设备数量: {len(env.devices)}")
        print(f"  服务器数量: {len(env.servers)}")
        print(f"  状态空间维度: {env.state_dim}")
        print(f"  动作空间维度: {env.action_dim}")
    except Exception as e:
        print(f"✗ 环境初始化失败: {e}")
        return False
    
    print("\n2. 网络条件更新测试")
    print("-" * 30)
    
    try:
        states = env.reset()
        print("✓ 环境重置成功")
        
        # 检查网络条件
        for i, condition in enumerate(env.network_conditions):
            device_pos = condition['device_position']
            server_rates = condition['server_rates']
            print(f"  设备 {i}: 位置{device_pos}, 速率{[f'{r/1e6:.1f}Mbps' for r in server_rates]}")
            
            # 验证速率是否合理
            for rate in server_rates:
                if rate < 0 or rate > 1e9:  # 0-1Gbps范围检查
                    print(f"  ⚠ 异常速率: {rate/1e6:.1f}Mbps")
        
        print("✓ 网络条件更新正常")
    except Exception as e:
        print(f"✗ 网络条件更新失败: {e}")
        return False
    
    print("\n3. 动作执行测试")
    print("-" * 30)
    
    try:
        # 测试随机动作
        actions = [np.random.uniform(-1, 1, env.action_dim) for _ in range(env.num_devices)]
        next_states, rewards, dones, info = env.step(actions)
        
        print("✓ 动作执行成功")
        print(f"  奖励: {[f'{r:.3f}' for r in rewards]}")
        print(f"  完成标志: {dones}")
        
        # 检查执行结果
        if 'execution_results' in info:
            exec_results = info['execution_results']
            if 'device_delays' in exec_results:
                delays = exec_results['device_delays']
                print(f"  延迟: {[f'{d:.4f}s' for d in delays]}")
            if 'device_energies' in exec_results:
                energies = exec_results['device_energies']
                print(f"  能耗: {[f'{e:.4f}J' for e in energies]}")
        
        print("✓ 执行结果正常")
    except Exception as e:
        print(f"✗ 动作执行失败: {e}")
        return False
    
    return True

def test_baseline_algorithms():
    """测试基线算法"""
    print("\n4. 基线算法测试")
    print("-" * 30)
    
    try:
        env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
        states = env.reset()
        
        # 测试MaxFlowMinCut算法
        print("测试MaxFlowMinCut算法:")
        maxflow_baseline = MaxFlowMinCutBaseline()
        
        actions = maxflow_baseline.get_actions(
            env.devices, env.tasks, env.servers, env.network_conditions
        )
        
        print(f"  动作数量: {len(actions)}")
        for i, action in enumerate(actions):
            print(f"  设备{i}: 分割点={action['partition_point']}, "
                  f"服务器={action['server_id']}, 频率={action['frequency']/1e9:.2f}GHz")
        
        print("✓ MaxFlowMinCut算法正常")
        
        # 测试FullOffload算法
        print("\n测试FullOffload算法:")
        offload_baseline = FullOffloadBaseline()
        
        actions = offload_baseline.get_actions(
            env.devices, env.tasks, env.servers
        )
        
        print(f"  动作数量: {len(actions)}")
        for i, action in enumerate(actions):
            print(f"  设备{i}: 分割点={action['partition_point']}, "
                  f"服务器={action['server_id']}, 频率={action['frequency']/1e9:.2f}GHz")
        
        print("✓ FullOffload算法正常")
        
    except Exception as e:
        print(f"✗ 基线算法测试失败: {e}")
        return False
    
    return True

def test_shannon_capacity_properties():
    """测试香农容量特性"""
    print("\n5. 香农容量特性测试")
    print("-" * 30)
    
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    # 测试距离影响
    print("距离影响测试:")
    test_distances = [10, 100, 500, 1000]
    
    for distance in test_distances:
        capacities = []
        for _ in range(10):
            capacity = env._calculate_shannon_capacity(0, 0, distance, 0)
            if capacity > 0:
                capacities.append(capacity / 1e6)
        
        if capacities:
            avg_capacity = np.mean(capacities)
            print(f"  {distance:4d}m: {avg_capacity:6.2f} Mbps (平均)")
        else:
            print(f"  {distance:4d}m: 无法通信")
    
    # 测试随机性
    print("\n随机性测试 (固定距离100m):")
    capacities_100m = []
    for _ in range(20):
        capacity = env._calculate_shannon_capacity(0, 0, 100, 0)
        if capacity > 0:
            capacities_100m.append(capacity / 1e6)
    
    if capacities_100m:
        print(f"  平均容量: {np.mean(capacities_100m):.2f} Mbps")
        print(f"  标准差: {np.std(capacities_100m):.2f} Mbps")
        print(f"  变异系数: {np.std(capacities_100m)/np.mean(capacities_100m):.3f}")
        print("✓ 随机性正常")
    else:
        print("✗ 无有效容量计算")
        return False
    
    return True

def test_performance_comparison():
    """性能对比测试"""
    print("\n6. 性能对比测试")
    print("-" * 30)
    
    try:
        env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
        
        # 运行多个episode测试
        total_rewards = []
        total_delays = []
        total_energies = []
        
        for episode in range(5):
            states = env.reset()
            
            # 使用随机动作
            actions = [np.random.uniform(-1, 1, env.action_dim) for _ in range(env.num_devices)]
            next_states, rewards, dones, info = env.step(actions)
            
            total_rewards.extend(rewards)
            
            if 'execution_results' in info:
                exec_results = info['execution_results']
                if 'device_delays' in exec_results:
                    total_delays.extend(exec_results['device_delays'])
                if 'device_energies' in exec_results:
                    total_energies.extend(exec_results['device_energies'])
        
        print(f"测试结果 (5个episodes):")
        print(f"  平均奖励: {np.mean(total_rewards):.3f}")
        print(f"  平均延迟: {np.mean(total_delays):.4f}s")
        print(f"  平均能耗: {np.mean(total_energies):.4f}J")
        print("✓ 性能测试完成")
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("香农容量模型系统集成测试")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 运行所有测试
    tests = [
        test_environment_integration,
        test_baseline_algorithms,
        test_shannon_capacity_properties,
        test_performance_comparison
    ]
    
    for test_func in tests:
        if not test_func():
            all_tests_passed = False
    
    print("\n" + "="*60)
    if all_tests_passed:
        print("🎉 所有测试通过！香农容量模型集成成功！")
        print("系统已准备好进行训练和评估。")
    else:
        print("❌ 部分测试失败，请检查系统配置。")
    print("="*60)

if __name__ == "__main__":
    main()
