"""
Multi-Agent Edge Computing Environment for DNN Inference Optimization
Implements the RL environment with proper state/action spaces and reward design
"""

import numpy as np
import random
from typing import Dict, List, Tuple, Any, Optional
from config import config
from models import LocalDevice, EdgeServer, EdgeInferenceTask
from performance import EnergyModel, DelayModel, AdaptiveWeightMechanism
from resnet_layer_info import get_output_size_at_layer

class EdgeComputingEnvironment:
    """
    Multi-agent environment for heterogeneous edge computing optimization
    Implements battery-aware DNN inference with early exits and partitioning
    """
    
    def __init__(self, num_devices: int = None, num_servers: int = None, 
                 random_seed: int = None):
        """
        Initialize the edge computing environment
        
        Args:
            num_devices: Number of devices (agents)
            num_servers: Number of edge servers
            random_seed: Random seed for reproducibility
        """
        if random_seed is not None:
            np.random.seed(random_seed)
            random.seed(random_seed)
        
        # Environment configuration
        self.num_devices = num_devices or config.environment.num_devices
        self.num_servers = num_servers or config.server.num_servers
        self.max_steps = config.environment.max_steps
        
        # Initialize devices with random positions
        self.devices = [
            LocalDevice(device_id=i, initial_battery_ratio=np.random.uniform(0.5, 1.0))
            for i in range(self.num_devices)
        ]

        # Initialize servers with random positions
        self.servers = [
            EdgeServer(server_id=i)
            for i in range(self.num_servers)
        ]
        
        # Performance models
        self.energy_model = EnergyModel()
        self.delay_model = DelayModel()
        self.weight_mechanism = AdaptiveWeightMechanism()
        
        # Environment state
        self.current_step = 0
        self.tasks = []
        self.network_conditions = []
        
        # Action and state space dimensions
        self.state_dim = self._calculate_state_dim()
        self.action_dim = self._calculate_action_dim()
        
        # Normalization factors
        self.max_delay_norm = config.environment.max_delay_normalization
        self.max_energy_norm = config.environment.max_energy_normalization
        
        # Episode statistics
        self.episode_stats = {
            'total_energy': 0.0,
            'total_delay': 0.0,
            'accuracy_violations': 0,
            'delay_violations': 0,
            'battery_depletions': 0
        }
    
    def _calculate_state_dim(self) -> int:
        """Calculate state space dimension for each agent"""
        # Local state: [battery_ratio, frequency_ratio, x_normalized, y_normalized]
        local_state_dim = 4

        # Global state components: server states with position and load info
        # [queue_utilization, processing_utilization, x_normalized, y_normalized] per server
        global_state_dim = self.num_servers * 4

        # Task state: accuracy requirement
        task_state_dim = 1

        return local_state_dim + global_state_dim + task_state_dim
    
    def _calculate_action_dim(self) -> int:
        """Calculate action space dimension for each agent"""
        # Actions: [partition_point_ratio, server_selection, frequency_ratio]
        # partition_point_ratio: continuous [0, 1] (mapped to valid partition points)
        # server_selection: continuous [-1, 1] (mapped to discrete server IDs)
        # frequency_ratio: continuous [0, 1] (mapped to frequency range)
        return 3
    
    def reset(self, accuracy_requirements: List[float] = None) -> List[np.ndarray]:
        """
        Reset environment to initial state
        
        Args:
            accuracy_requirements: List of accuracy requirements for each device
            
        Returns:
            Initial states for all agents
        """
        # Reset devices
        for device in self.devices:
            device.reset(initial_battery_ratio=np.random.uniform(0.5, 1.0))
        
        # Reset servers
        for server in self.servers:
            server.reset()
        
        # Create new tasks
        if accuracy_requirements is None:
            accuracy_requirements = [
                np.random.uniform(*config.optimization.acc_min_range)
                for _ in range(self.num_devices)
            ]
        
        self.tasks = [
            EdgeInferenceTask(task_id=i, accuracy_requirement=acc_req)
            for i, acc_req in enumerate(accuracy_requirements)
        ]
        
        # Reset network conditions
        self._update_network_conditions()
        
        # Reset environment state
        self.current_step = 0
        self.episode_stats = {
            'total_energy': 0.0,
            'total_delay': 0.0,
            'accuracy_violations': 0,
            'delay_violations': 0,
            'battery_depletions': 0
        }
        
        return self._get_states()
    
    def step(self, actions: List[np.ndarray]) -> Tuple[List[np.ndarray], List[float], 
                                                      List[bool], Dict[str, Any]]:
        """
        Execute one step in the environment
        
        Args:
            actions: List of actions for each agent
            
        Returns:
            Tuple of (next_states, rewards, dones, info)
        """
        decoded_actions = self._decode_actions(actions)
        return self._execute_and_get_next_state(decoded_actions)

    def step_with_decoded_actions(self, decoded_actions: List[Dict[str, Any]]) -> Tuple[List[np.ndarray], List[float], List[bool], Dict[str, Any]]:
        """
        Execute a step with already decoded actions. Used for baseline evaluations.
        """
        return self._execute_and_get_next_state(decoded_actions)

    def _execute_and_get_next_state(self, decoded_actions: List[Dict[str, Any]]):
        """Helper function to run a step and update the environment."""
        # Execute tasks with current configuration
        execution_results = self._execute_tasks(decoded_actions)
        
        # Calculate rewards
        rewards = self._calculate_rewards(execution_results)
        
        # Update environment state
        self._update_environment_state(execution_results)
        
        # Check if episode is done
        self.current_step += 1
        dones = self._check_done()
        
        # Get next states
        next_states = self._get_states()
        
        # Prepare info dictionary
        info = self._get_info(execution_results, decoded_actions)
        
        return next_states, rewards, dones, info
    
    def _decode_actions(self, actions: List[np.ndarray]) -> List[Dict[str, Any]]:
        """
        Decode continuous actions to discrete/continuous values
        
        Args:
            actions: List of raw action arrays
            
        Returns:
            List of decoded action dictionaries
        """
        decoded_actions = []
        
        for i, action in enumerate(actions):
            task = self.tasks[i]
            device = self.devices[i]
            
            # Decode partition point (continuous to discrete)
            partition_ratio = (action[0] + 1.0) / 2.0  # Map [-1,1] to [0,1]

            # Include full offload option in available choices
            valid_partitions = task.valid_partition_points + [task.early_exit_point + 1]
            valid_partitions = sorted(list(set(valid_partitions)))  # Remove duplicates and sort

            if len(valid_partitions) > 1:
                partition_idx = int(partition_ratio * (len(valid_partitions) - 1))
                partition_point = valid_partitions[partition_idx]
            else:
                partition_point = 0  # Full local execution
            
            # Decode server selection (continuous to discrete)
            server_ratio = (action[1] + 1.0) / 2.0  # Map [-1,1] to [0,1]
            server_id = int(server_ratio * self.num_servers)
            server_id = min(server_id, self.num_servers - 1)
            
            # Decode frequency (continuous to continuous)
            freq_ratio = (action[2] + 1.0) / 2.0  # Map [-1,1] to [0,1]
            frequency = (device.f_min + freq_ratio * (device.f_max - device.f_min))
            
            decoded_actions.append({
                'partition_point': partition_point,
                'server_id': server_id,
                'frequency': frequency,
                'partition_ratio': partition_ratio,
                'server_ratio': server_ratio,
                'freq_ratio': freq_ratio
            })
        
        return decoded_actions
    
    def _execute_tasks(self, decoded_actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Execute tasks with given actions
        
        Args:
            decoded_actions: List of decoded action dictionaries
            
        Returns:
            Execution results dictionary
        """
        device_delays = []
        device_energies = []
        server_allocations = []
        constraint_violations = []
        
        for i, (action, task, device) in enumerate(zip(decoded_actions, self.tasks, self.devices)):
            # Set task configuration
            task.set_partition_point(action['partition_point'])
            task.set_target_server(action['server_id'])
            task.set_device_frequency(action['frequency'])
            device.set_frequency(action['frequency'])
            
            # Get computational requirements
            local_flops = task.get_local_flops()
            edge_flops = task.get_edge_flops()
            data_size = task.get_transmission_data_size()
            
            # Get server and network parameters
            server = self.servers[action['server_id']]
            network_condition = self.network_conditions[i]

            # Get transmission rate to the selected server
            transmission_rate = network_condition['server_rates'][action['server_id']]

            # Calculate base delay (without queuing)
            delay_result = self.delay_model.calculate_total_delay(
                local_flops=local_flops,
                edge_flops=edge_flops,
                data_size_bits=data_size,
                device_frequency=action['frequency'],
                server_frequency=server.f_server,
                transmission_rate=transmission_rate
            )

            # Add queuing delay if there's edge computation
            queuing_delay = 0.0
            if edge_flops > 0:
                queuing_delay = server.calculate_queuing_delay()
            
            # Total delay includes base delay + queuing delay
            total_delay = delay_result['total_delay'] + queuing_delay
            device_delays.append(total_delay)
            
            # Calculate energy (using the transmission rate to selected server)
            energy_result = self.energy_model.calculate_total_device_energy(
                local_flops=local_flops,
                device_frequency=action['frequency'],
                g_device=device.g_device,
                data_size_bits=data_size,
                transmission_rate=transmission_rate,
                total_time=total_delay,
                kappa=device.kappa,
                tx_power=device.p_tx,
                static_power=device.p_static
            )
            
            total_energy = energy_result['total_energy']
            device_energies.append(total_energy)
            
            # Server allocation (simplified without capacity constraints)
            if edge_flops > 0:
                # Calculate estimated edge processing time
                edge_time = delay_result.get('edge_delay', 0.0)
                server_allocated = server.allocate_task(
                    task_id=task.task_id,
                    required_flops=edge_flops,
                    device_id=device.device_id,
                    estimated_time=edge_time
                )
            else:
                server_allocated = True  # No server needed for local execution
            server_allocations.append(server_allocated)
            
            # Check constraints (now includes server capacity)
            violations = {
                'accuracy': not task.is_accuracy_satisfied(),
                'delay': total_delay > config.optimization.t_max,
                'battery': not device.consume_energy(total_energy),
                'server_capacity': not server_allocated  # True if server rejected the task
            }
            constraint_violations.append(violations)
        
        return {
            'device_delays': device_delays,
            'device_energies': device_energies,
            'server_allocations': server_allocations,
            'constraint_violations': constraint_violations,
            'delay_results': delay_result,
            'energy_results': energy_result,
            'queuing_delays': [server.calculate_queuing_delay() for server in self.servers]
        }
    
    def _calculate_rewards(self, execution_results: Dict[str, Any]) -> List[float]:
        """
        Calculate individual rewards for each agent
        
        Args:
            execution_results: Results from task execution
            
        Returns:
            List of rewards for each agent
        """
        rewards = []
        device_delays = execution_results['device_delays']
        device_energies = execution_results['device_energies']
        violations = execution_results['constraint_violations']
        
        for i, (device, task) in enumerate(zip(self.devices, self.tasks)):
            # Get adaptive weight
            alpha = device.get_adaptive_weight()
            
            # Normalize delay and energy
            normalized_delay = device_delays[i] 
            normalized_energy = device_energies[i] 
            
            # Base reward (negative objective function)
            base_reward = 0.01/(alpha * normalized_delay + (1 - alpha) * normalized_energy)
            
            # Constraint penalties
            penalty = 0.0
            
            if violations[i]['accuracy']:
                penalty += config.environment.accuracy_violation_penalty
                self.episode_stats['accuracy_violations'] += 1
            
            if violations[i]['delay']:
                penalty += config.environment.delay_violation_penalty
                self.episode_stats['delay_violations'] += 1
            
            if violations[i]['battery']:
                penalty += config.environment.battery_depletion_penalty
                self.episode_stats['battery_depletions'] += 1

            if violations[i]['server_capacity']:
                penalty += 50.0  # Server capacity violation penalty
                # Note: server capacity violations are tracked in episode stats if needed
            
            # Final reward
            reward = base_reward - penalty
            rewards.append(reward)
        
        return rewards
    
    def _get_states(self) -> List[np.ndarray]:
        """Get current states for all agents"""
        states = []
        
        for i, (device, task) in enumerate(zip(self.devices, self.tasks)):
            # Local state
            local_state = device.get_state()
            
            # Global state (server information)
            server_states = []
            for server in self.servers:
                server_state = server.get_state()
                server_states.extend(server_state)
            
            # Task state
            task_state = [task.accuracy_requirement]
            
            # Combine all state components
            full_state = np.concatenate([
                local_state,
                np.array(server_states),
                np.array(task_state)
            ])
            
            states.append(full_state.astype(np.float32))
        
        return states
    
    def _calculate_shannon_capacity(self, device_x: float, device_y: float,
                                   server_x: float, server_y: float) -> float:
        """
        Calculate transmission rate using correct Shannon capacity formula
        Rn = bn × log2(1 + (pn × hn) / (bn × N0))

        Args:
            device_x, device_y: Device position
            server_x, server_y: Server position

        Returns:
            Transmission rate in bps
        """
        # Calculate distance
        distance = np.sqrt((device_x - server_x)**2 + (device_y - server_y)**2)

        # Avoid division by zero
        distance = max(distance, config.physical.reference_distance)

        # Check maximum distance constraint
        if distance > config.physical.max_distance:
            return 0.0  # No communication beyond max distance

        # Random bandwidth within range
        bn = np.random.uniform(
            config.network.bandwidth_min,
            config.network.bandwidth_max
        )  # Hz

        # Fixed transmit power (1W = 30 dBm)
        pn_watts = config.network.tx_power_watts  # 1 W

        # Calculate channel gain: hn = K / d^n
        # Apply path loss exponent
        channel_gain_linear = (
            config.physical.channel_gain_constant /
            (distance ** config.physical.path_loss_exponent)
        )

        # Add random fading if enabled
        if config.physical.enable_fading:
            fading_db = np.random.normal(0, config.physical.fading_std_db)
            fading_linear = 10 ** (fading_db / 10)
            channel_gain_linear *= fading_linear

        # Convert channel gain to dB for constraint checking
        if channel_gain_linear <= 0:
            return 0.0

        channel_gain_db = 10 * np.log10(channel_gain_linear)

        # Check minimum channel gain constraint
        if channel_gain_db < config.physical.min_channel_gain_db:
            return 0.0  # No communication below minimum channel gain

        # Noise power spectral density in linear scale (W/Hz)
        N0_dbm_hz = config.network.noise_psd_dbm_hz
        N0_watts_hz = 10 ** ((N0_dbm_hz - 30) / 10)  # Convert dBm/Hz to W/Hz

        # Shannon capacity formula: Rn = bn × log2(1 + (pn × hn) / (bn × N0))
        snr_linear = (pn_watts * channel_gain_linear) / (bn * N0_watts_hz)

        if snr_linear <= 0:
            return 0.0

        capacity_bps = bn * np.log2(1 + snr_linear)

        return capacity_bps

    def _update_network_conditions(self):
        """Update network conditions for all devices based on their positions"""
        self.network_conditions = []

        for i in range(self.num_devices):
            device = self.devices[i]
            device_x, device_y = device.get_position()

            # Calculate rates to all servers using Shannon capacity
            server_rates = []
            for server in self.servers:
                server_x, server_y = server.get_position()
                rate = self._calculate_shannon_capacity(
                    device_x, device_y, server_x, server_y
                )
                server_rates.append(rate)

            condition = {
                'server_rates': server_rates,  # List of rates to each server
                'device_position': (device_x, device_y)
            }
            self.network_conditions.append(condition)
    
    def _update_environment_state(self, execution_results: Dict[str, Any]):
        """Update environment state after execution"""
        # Update episode statistics
        self.episode_stats['total_energy'] += sum(execution_results['device_energies'])
        self.episode_stats['total_delay'] += sum(execution_results['device_delays'])
        
        # Release completed tasks from servers (improved task lifecycle management)
        self._release_completed_tasks(execution_results)
        
        # Update device and server histories
        for device in self.devices:
            device.update_history()
        
        for server in self.servers:
            server.update_history()
        
        # Update network conditions for next step
        self._update_network_conditions()
    
    def _release_completed_tasks(self, execution_results: Dict[str, Any]):
        """
        Release completed tasks from servers to free up capacity
        Inspired by original project's task lifecycle management
        """
        for i, task in enumerate(self.tasks):
            # If task had edge computation and was successfully allocated
            if (execution_results['server_allocations'][i] and 
                task.get_edge_flops() > 0):
                
                server = self.servers[task.target_server]
                # Release the task from server after execution
                server.deallocate_task(task.task_id)
    
    def _check_done(self) -> List[bool]:
        """Check if episode is done for each agent"""
        # Episode ends if max steps reached or all devices depleted
        max_steps_reached = self.current_step >= self.max_steps
        all_depleted = all(device.is_depleted() for device in self.devices)
        
        episode_done = max_steps_reached or all_depleted
        
        return [episode_done] * self.num_devices
    
    def _get_info(self, execution_results: Dict[str, Any], 
                  decoded_actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get comprehensive information about the current step"""
        return {
            'step': self.current_step,
            'device_states': [device.get_info() for device in self.devices],
            'server_states': [server.get_info() for server in self.servers],
            'task_states': [task.get_info() for task in self.tasks],
            'actions': decoded_actions,
            'execution_results': execution_results,
            'episode_stats': self.episode_stats.copy(),
            'network_conditions': self.network_conditions.copy()
        }
    
    def get_env_info(self) -> Dict[str, Any]:
        """Get comprehensive environment information"""
        return {
            'num_devices': self.num_devices,
            'num_servers': self.num_servers,
            'state_dim': self.state_dim,
            'action_dim': self.action_dim,
            'max_steps': self.max_steps,
            'current_step': self.current_step,
            'episode_stats': self.episode_stats.copy()
        }
