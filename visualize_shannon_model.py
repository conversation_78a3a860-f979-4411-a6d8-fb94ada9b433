#!/usr/bin/env python3
"""
可视化香农容量通信模型
Visualize Shannon Capacity Communication Model
"""

import numpy as np
import matplotlib.pyplot as plt
from config import config
from environment import EdgeComputingEnvironment

def create_shannon_visualization():
    """创建香农容量模型可视化"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Shannon Capacity Communication Model Analysis', fontsize=16, fontweight='bold')
    
    # 1. 距离-容量关系图
    ax1 = axes[0, 0]
    distances = np.linspace(1, 1500, 100)
    
    # 创建环境用于计算
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    # 计算不同距离下的平均容量
    avg_capacities = []
    min_capacities = []
    max_capacities = []
    
    for distance in distances:
        capacities = []
        for _ in range(20):  # 每个距离采样20次
            capacity = env._calculate_shannon_capacity(0, 0, distance, 0)
            if capacity > 0:
                capacities.append(capacity / 1e6)  # 转换为Mbps
        
        if capacities:
            avg_capacities.append(np.mean(capacities))
            min_capacities.append(np.min(capacities))
            max_capacities.append(np.max(capacities))
        else:
            avg_capacities.append(0)
            min_capacities.append(0)
            max_capacities.append(0)
    
    ax1.fill_between(distances, min_capacities, max_capacities, alpha=0.3, color='blue', label='Capacity Range')
    ax1.plot(distances, avg_capacities, 'b-', linewidth=2, label='Average Capacity')
    ax1.set_xlabel('Distance (m)')
    ax1.set_ylabel('Capacity (Mbps)')
    ax1.set_title('Distance vs Shannon Capacity')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. SNR-容量关系图
    ax2 = axes[0, 1]
    snr_db_range = np.linspace(-10, 50, 100)
    theoretical_capacities = []
    
    for snr_db in snr_db_range:
        snr_linear = 10 ** (snr_db / 10)
        capacity_mbps = config.network.bandwidth * np.log2(1 + snr_linear) / 1e6
        theoretical_capacities.append(capacity_mbps)
    
    ax2.plot(snr_db_range, theoretical_capacities, 'r-', linewidth=2, label='Shannon Capacity')
    ax2.axvline(x=config.physical.min_snr_db, color='g', linestyle='--', label=f'Min SNR ({config.physical.min_snr_db} dB)')
    ax2.set_xlabel('SNR (dB)')
    ax2.set_ylabel('Capacity (Mbps)')
    ax2.set_title('SNR vs Shannon Capacity')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 3. 发射功率影响
    ax3 = axes[1, 0]
    tx_powers = np.linspace(10, 40, 100)
    distance_fixed = 200  # 固定距离200m
    
    capacities_by_power = []
    for tx_power in tx_powers:
        # 手动计算容量（固定其他参数）
        path_loss_db = (
            config.physical.path_loss_at_reference +
            10 * config.physical.path_loss_exponent * 
            np.log10(distance_fixed / config.physical.reference_distance)
        )
        rx_power_dbm = tx_power - path_loss_db
        snr_db = rx_power_dbm - config.network.noise_power_dbm
        
        if snr_db >= config.physical.min_snr_db:
            snr_linear = 10 ** (snr_db / 10)
            capacity_mbps = config.network.bandwidth * np.log2(1 + snr_linear) / 1e6
        else:
            capacity_mbps = 0
        
        capacities_by_power.append(capacity_mbps)
    
    ax3.plot(tx_powers, capacities_by_power, 'g-', linewidth=2, label=f'Distance = {distance_fixed}m')
    ax3.axvspan(config.network.tx_power_dbm_min, config.network.tx_power_dbm_max, 
                alpha=0.2, color='orange', label='Typical Tx Power Range')
    ax3.set_xlabel('Transmit Power (dBm)')
    ax3.set_ylabel('Capacity (Mbps)')
    ax3.set_title('Transmit Power vs Capacity')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. 路径损耗指数影响
    ax4 = axes[1, 1]
    path_loss_exponents = np.linspace(1.5, 4.0, 100)
    distance_fixed = 500  # 固定距离500m
    tx_power_fixed = 25   # 固定发射功率25dBm
    
    capacities_by_exponent = []
    for exponent in path_loss_exponents:
        path_loss_db = (
            config.physical.path_loss_at_reference +
            10 * exponent * np.log10(distance_fixed / config.physical.reference_distance)
        )
        rx_power_dbm = tx_power_fixed - path_loss_db
        snr_db = rx_power_dbm - config.network.noise_power_dbm
        
        if snr_db >= config.physical.min_snr_db:
            snr_linear = 10 ** (snr_db / 10)
            capacity_mbps = config.network.bandwidth * np.log2(1 + snr_linear) / 1e6
        else:
            capacity_mbps = 0
        
        capacities_by_exponent.append(capacity_mbps)
    
    ax4.plot(path_loss_exponents, capacities_by_exponent, 'm-', linewidth=2, 
             label=f'Distance = {distance_fixed}m, Tx = {tx_power_fixed}dBm')
    ax4.axvline(x=2.0, color='r', linestyle='--', label='Free Space (n=2.0)')
    ax4.axvline(x=3.5, color='b', linestyle='--', label='Urban (n=3.5)')
    ax4.set_xlabel('Path Loss Exponent')
    ax4.set_ylabel('Capacity (Mbps)')
    ax4.set_title('Path Loss Exponent vs Capacity')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    return fig

def create_model_comparison():
    """创建新旧模型对比图"""
    
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('Communication Model Comparison: Old vs New', fontsize=16, fontweight='bold')
    
    distances = np.linspace(1, 1500, 100)
    
    # 旧模型（指数衰减）
    old_rates = []
    base_rate = 100e6  # 100 Mbps基础速率
    
    for distance in distances:
        rate_factor = np.exp(-0.001 * distance)  # 使用旧的衰减因子
        rate_factor = max(rate_factor, 0.3)  # 最小速率比例
        if distance > 1500:
            rate_factor = 0.3
        old_rates.append(base_rate * rate_factor / 1e6)
    
    # 新模型（香农容量）- 使用平均值
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    new_rates = []
    
    for distance in distances:
        capacities = []
        for _ in range(10):  # 采样10次取平均
            capacity = env._calculate_shannon_capacity(0, 0, distance, 0)
            if capacity > 0:
                capacities.append(capacity / 1e6)
        
        if capacities:
            new_rates.append(np.mean(capacities))
        else:
            new_rates.append(0)
    
    # 绘制对比图
    ax1.plot(distances, old_rates, 'r-', linewidth=2, label='Old Model (Exponential Decay)')
    ax1.plot(distances, new_rates, 'b-', linewidth=2, label='New Model (Shannon Capacity)')
    ax1.set_xlabel('Distance (m)')
    ax1.set_ylabel('Data Rate (Mbps)')
    ax1.set_title('Distance vs Data Rate Comparison')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 模型特征对比
    features = ['Physical\nRealism', 'Parameter\nRichness', 'Randomness', 'Computational\nComplexity', 'Theoretical\nBasis']
    old_scores = [2, 2, 1, 1, 2]  # 1-5分
    new_scores = [5, 5, 4, 3, 5]
    
    x = np.arange(len(features))
    width = 0.35
    
    ax2.bar(x - width/2, old_scores, width, label='Old Model', color='red', alpha=0.7)
    ax2.bar(x + width/2, new_scores, width, label='New Model', color='blue', alpha=0.7)
    
    ax2.set_xlabel('Model Features')
    ax2.set_ylabel('Score (1-5)')
    ax2.set_title('Model Feature Comparison')
    ax2.set_xticks(x)
    ax2.set_xticklabels(features)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for i, (old, new) in enumerate(zip(old_scores, new_scores)):
        ax2.text(i - width/2, old + 0.1, str(old), ha='center', va='bottom')
        ax2.text(i + width/2, new + 0.1, str(new), ha='center', va='bottom')
    
    plt.tight_layout()
    return fig

def print_model_summary():
    """打印模型总结"""
    print("="*80)
    print("香农容量通信模型重构总结")
    print("="*80)
    
    print("\n🔄 重构内容:")
    print("-" * 50)
    print("1. 配置文件更新 (config.py):")
    print("   • 新增香农容量相关参数")
    print("   • 带宽、发射功率、噪声功率等")
    print("   • 路径损耗模型参数")
    print("   • 阴影衰落参数")
    
    print("\n2. 通信模型重构 (environment/edge_env.py):")
    print("   • _calculate_distance_based_rate() → _calculate_shannon_capacity()")
    print("   • 实现完整的香农容量计算")
    print("   • 路径损耗、SNR、容量计算")
    
    print("\n📊 新模型特点:")
    print("-" * 50)
    print("✓ 理论基础: 信息论香农容量公式")
    print("✓ 物理真实: 考虑发射功率、路径损耗、噪声")
    print("✓ 参数丰富: 带宽、功率、路径损耗指数等")
    print("✓ 随机性: 发射功率随机、阴影衰落")
    print("✓ 约束合理: 最小SNR阈值、最大距离限制")
    
    print("\n🎯 应用优势:")
    print("-" * 50)
    print("• 更准确的无线通信建模")
    print("• 支持更多参数调节和优化")
    print("• 为强化学习提供更真实的环境")
    print("• 便于与实际系统对比验证")
    
    print("\n⚙️ 关键参数:")
    print("-" * 50)
    print(f"• 信道带宽: {config.network.bandwidth/1e6:.1f} MHz")
    print(f"• 发射功率: {config.network.tx_power_dbm_min}-{config.network.tx_power_dbm_max} dBm")
    print(f"• 噪声功率: {config.network.noise_power_dbm} dBm")
    print(f"• 路径损耗指数: {config.physical.path_loss_exponent}")
    print(f"• 最小SNR: {config.physical.min_snr_db} dB")

def main():
    """主函数"""
    print("香农容量通信模型可视化")
    print("=" * 80)
    
    # 创建可视化
    print("正在生成香农容量模型分析图...")
    fig1 = create_shannon_visualization()
    fig1.savefig('shannon_capacity_analysis.png', dpi=300, bbox_inches='tight')
    print("已保存: shannon_capacity_analysis.png")
    
    print("\n正在生成模型对比图...")
    fig2 = create_model_comparison()
    fig2.savefig('communication_model_comparison.png', dpi=300, bbox_inches='tight')
    print("已保存: communication_model_comparison.png")
    
    # 打印总结
    print_model_summary()
    
    print("\n" + "="*80)
    print("香农容量通信模型重构完成！")
    print("="*80)

if __name__ == "__main__":
    main()
