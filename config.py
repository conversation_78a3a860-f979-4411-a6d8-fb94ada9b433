"""
Configuration file for Heterogeneous Multi-Edge Server DNN Inference Optimization Framework
Based on the technical specification document requirements
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Dict, Tuple

@dataclass
class DeviceConfig:
    """Local device configuration parameters"""
    # Battery parameters
    max_battery: float = 0.12
    min_battery: float = 0.08 # mAh (30-50 range)
    min_battery_ratio: float = 0.05  # B_min = 0.05 * B_max
    voltage: float = 5.0  # V
    max_energy: float = 0.72  # J (4mAh * 5V * 3.6 = 72J)
    
    # GPU frequency parameters
    f_min: float = 0.12e9  # Hz (0.12 GHz)
    f_max: float = 1.10e9  # Hz (1.10 GHz)
    
    # Computing capability
    g_device: float = 153.0  # FLOPS per cycle
    
    # Energy parameters
    kappa: float = 1.3  # W/GHz^3 (energy coefficient)
    p_static_min: float = 0.03  # W (static power range)
    p_static_max: float = 0.08  # W
    
    # Communication parameters
    p_tx_min: float = 0.093  # W (transmission power range)
    p_tx_max: float = 0.115  # W
    
    # Device type
    device_type: str = "jetson_xavier"

@dataclass
class ServerConfig:
    """Edge server configuration with queuing and resource competition"""
    # Server frequency range
    f_server_min: float = 1.6e9  # Hz (2.2 GHz)
    f_server_max: float = 2.6e9  # Hz (2.8 GHz)

    # Computing capability
    g_server: float = 380.0  # FLOPS per cycle

    # Server configuration
    num_servers: int = 4
    server_type: str = "laptop_4060"

    # Queue and resource management
    max_queue_size: int = 10  # maximum number of tasks in queue
    service_rate: float = 2.0  # tasks per second (average service rate)
    queue_discipline: str = "FIFO"  # First In First Out

    # Resource competition parameters
    enable_resource_competition: bool = True
    processing_overhead: float = 0.1  # 10% overhead for context switching

@dataclass
class NetworkConfig:
    """Network environment configuration"""
    # Transmission rate parameters (base rates)
    rate_min: float = 85.5e6  # bps (15.5 Mbps)
    rate_max: float = 115.0e6  # bps (25.0 Mbps)

    # Data transmission parameters
    bits_per_float: int = 32  # FP32 model

@dataclass
class PhysicalConfig:
    """Physical deployment and distance-based communication configuration"""
    # Deployment area (rectangular region)
    area_width: float = 1000.0  # meters
    area_height: float = 1000.0  # meters

    # Distance-rate model parameters
    # Rate = base_rate * exp(-distance_decay * distance)
    distance_decay: float = 0.001  # decay factor per meter
    min_rate_ratio: float = 0.3  # minimum rate as ratio of base rate
    max_distance: float = 1500.0  # maximum effective communication distance (meters)

    # Path loss model parameters (alternative model)
    # Rate = base_rate * (reference_distance / distance)^path_loss_exponent
    reference_distance: float = 10.0  # meters
    path_loss_exponent: float = 2.0  # free space: 2.0, urban: 3-4

    # Communication model selection
    use_exponential_decay: bool = True  # True: exponential, False: path loss

@dataclass
class ResNetConfig:
    """ResNet-50 model configuration with early exits"""
    # Dataset
    dataset: str = "cifar10"
    input_shape: Tuple[int, int, int] = (3, 32, 32)
    num_classes: int = 10
    
    # Early exit points and accuracies (from resnet50_cifar_accuracy.txt)
    early_exit_points: Dict[str, int] = None
    early_exit_accuracies: Dict[str, float] = None
    
    # Valid partition points (Conv2d layers only)
    valid_partition_points: List[int] = None
    
    def __post_init__(self):
        # Early exit points mapping
        self.early_exit_points = {
            "Exit_1": 32,   # after layer1 - 77.00%
            "Exit_2": 73,  # after layer2 - 87.31%
            "Exit_3": 105,  # after layer3[2] - 90.38%
            "Exit_4": 135,  # after layer3[5] - 91.13%
            "Exit_5": 150,  # after layer4[0] - 91.21%
            "Full": 166     # complete model - 91.33%
        }
        
        self.early_exit_accuracies = {
            "Exit_1": 0.77,
            "Exit_2": 0.8731,
            "Exit_3": 0.9038,
            "Exit_4": 0.9113,
            "Exit_5": 0.9133,
            "Full": 0.9201
        }
        
        # Conv2d layer indices (valid partition points)
        self.valid_partition_points = [
            0, 4, 7, 10, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 41, 44, 47, 50,
            53, 56, 59, 62, 65, 68, 74, 77, 80, 82, 85, 88, 91, 94, 97, 100,
            106, 109, 112, 115, 118, 121, 124, 127, 130, 136, 139, 142, 144,
            147, 150, 153, 156, 159, 162
        ]

@dataclass
class OptimizationConfig:
    """Optimization problem configuration"""
    # Accuracy constraints
    acc_min_range: Tuple[float, float] = (0.7, 0.91)
    default_acc_min: float = 0.85
    
    # Delay constraints
    t_max: float = 0.1  # seconds (100ms)
    
    # Adaptive weight parameters
    beta: float = 10.0  # sensitivity parameter
    theta: float = 0.5  # threshold parameter

@dataclass
class MADDPGConfig:
    """MADDPG algorithm configuration"""
    # Network architecture
    actor_hidden_dims: List[int] = None
    critic_hidden_dims: List[int] = None
    dropout_rate: float = 0.1
    
    # Training parameters
    lr_actor: float = 1e-4
    lr_critic: float = 5e-4  # 5x actor learning rate
    gamma: float = 0.99  # discount factor
    tau: float = 0.005  # soft update parameter
    
    # Experience replay
    buffer_size: int = 10000
    batch_size: int = 64
    update_frequency: int = 3  # update every 3 steps
    warmup_steps: int = 200
    
    # Exploration noise
    initial_noise: float = 0.2
    final_noise: float = 0.04
    noise_decay_episodes: float = 0.8  # decay over 80% of episodes
    
    # Training episodes
    max_episodes: int = 200
    max_steps_per_episode: int = 100
    
    # Gradient clipping
    grad_clip: float = 1.0
    
    def __post_init__(self):
        self.actor_hidden_dims = [128, 64]
        self.critic_hidden_dims = [128, 64]

@dataclass
class SACConfig:
    """SAC algorithm configuration"""
    # Network architecture
    actor_hidden_dims: List[int] = None
    critic_hidden_dims: List[int] = None
    
    # Training parameters
    lr_actor: float = 3e-4
    lr_critic: float = 3e-4
    lr_alpha: float = 3e-4
    gamma: float = 0.99  # discount factor
    tau: float = 0.005  # soft update parameter
    
    # Entropy
    alpha: float = 0.2  # initial entropy coefficient
    target_entropy: float = -3.0 # action_dim, will be set automatically
    automatic_entropy_tuning: bool = True
    
    # Experience replay
    buffer_size: int = 100000
    batch_size: int = 128
    max_episodes: int = 1000
    
    def __post_init__(self):
        self.actor_hidden_dims = [256, 256]
        self.critic_hidden_dims = [256, 256]

@dataclass
class EnvironmentConfig:
    """Multi-agent environment configuration"""
    # Number of devices (agents)
    num_devices: int = 4
    
    # Simulation parameters
    max_steps: int = 100
    
    # Reward scaling factors
    max_delay_normalization: float = 1  # 50ms
    max_energy_normalization: float = 1  # Max device energy
    
    # Penalty parameters (server overload penalty removed)
    battery_depletion_penalty: float = 100.0
    delay_violation_penalty: float = 100.0
    accuracy_violation_penalty: float = 100.0

# Global configuration instance
class Config:
    """Global configuration class"""
    def __init__(self):
        self.device = DeviceConfig()
        self.server = ServerConfig()
        self.network = NetworkConfig()
        self.physical = PhysicalConfig()
        self.resnet = ResNetConfig()
        self.optimization = OptimizationConfig()
        self.maddpg = MADDPGConfig()
        self.sac = SACConfig()
        self.environment = EnvironmentConfig()
    
    def get_early_exit_for_accuracy(self, min_accuracy: float) -> str:
        """Get the minimum early exit point that satisfies accuracy requirement"""
        for exit_name in ["Exit_1", "Exit_2", "Exit_3", "Exit_4", "Exit_5", "Full"]:
            if self.resnet.early_exit_accuracies[exit_name] >= min_accuracy:
                return exit_name
        return "Full"  # fallback to full model
    
    def get_valid_partitions_for_exit(self, exit_point: int) -> List[int]:
        """Get valid partition points up to the specified exit point"""
        return [p for p in self.resnet.valid_partition_points if p <= exit_point]

# Create global config instance
config = Config()
