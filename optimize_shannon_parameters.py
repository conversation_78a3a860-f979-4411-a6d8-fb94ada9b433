#!/usr/bin/env python3
"""
优化香农公式参数以达到目标速率范围[52,108]Mbps
"""

import numpy as np
from config import config
from environment import EdgeComputingEnvironment

def test_parameter_combination(channel_gain_constant, bandwidth_min, bandwidth_max, 
                              fading_std, num_samples=2000):
    """测试参数组合的效果"""
    
    # 临时修改配置
    original_gain = config.physical.channel_gain_constant
    original_bw_min = config.network.bandwidth_min
    original_bw_max = config.network.bandwidth_max
    original_fading = config.physical.fading_std_db
    
    config.physical.channel_gain_constant = channel_gain_constant
    config.network.bandwidth_min = bandwidth_min
    config.network.bandwidth_max = bandwidth_max
    config.physical.fading_std_db = fading_std
    
    try:
        env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
        
        rates = []
        for _ in range(num_samples // 4):  # 每次更新网络条件产生4个速率
            env._update_network_conditions()
            for condition in env.network_conditions:
                for rate in condition['server_rates']:
                    if rate > 0:
                        rates.append(rate / 1e6)  # 转换为Mbps
        
        if rates:
            rates = np.array(rates)
            
            # 计算统计信息
            min_rate = np.min(rates)
            max_rate = np.max(rates)
            mean_rate = np.mean(rates)
            std_rate = np.std(rates)
            
            # 计算目标范围符合性
            in_range = np.sum((rates >= 52) & (rates <= 108))
            compliance = in_range / len(rates) * 100
            
            # 计算分布质量评分
            target_center = 80  # 目标中心点
            center_deviation = abs(mean_rate - target_center)
            
            # 综合评分
            score = compliance - center_deviation * 0.5 - std_rate * 0.1
            
            return {
                'rates': rates,
                'min_rate': min_rate,
                'max_rate': max_rate,
                'mean_rate': mean_rate,
                'std_rate': std_rate,
                'compliance': compliance,
                'score': score,
                'sample_count': len(rates)
            }
        else:
            return None
            
    finally:
        # 恢复原始配置
        config.physical.channel_gain_constant = original_gain
        config.network.bandwidth_min = original_bw_min
        config.network.bandwidth_max = original_bw_max
        config.physical.fading_std_db = original_fading

def optimize_parameters():
    """优化参数以达到最佳速率范围符合性"""
    print("="*80)
    print("香农公式参数优化 - 目标范围 [52, 108] Mbps")
    print("="*80)
    
    best_score = -1000
    best_params = None
    best_results = None
    
    # 参数搜索范围
    channel_gains = [1e-7, 1.5e-7, 2e-7, 2.5e-7, 3e-7, 3.5e-7, 4e-7]
    bandwidth_ranges = [
        (12e6, 20e6),   # 12-20 MHz
        (15e6, 25e6),   # 15-25 MHz
        (18e6, 28e6),   # 18-28 MHz
        (20e6, 30e6),   # 20-30 MHz
    ]
    fading_stds = [4.0, 5.0, 6.0, 7.0, 8.0]
    
    print("正在搜索最优参数组合...")
    print("参数组合数:", len(channel_gains) * len(bandwidth_ranges) * len(fading_stds))
    print()
    
    test_count = 0
    total_tests = len(channel_gains) * len(bandwidth_ranges) * len(fading_stds)
    
    for gain in channel_gains:
        for bw_min, bw_max in bandwidth_ranges:
            for fading in fading_stds:
                test_count += 1
                
                if test_count % 10 == 0:
                    print(f"进度: {test_count}/{total_tests} ({test_count/total_tests*100:.1f}%)")
                
                results = test_parameter_combination(gain, bw_min, bw_max, fading)
                
                if results and results['score'] > best_score:
                    best_score = results['score']
                    best_params = {
                        'channel_gain_constant': gain,
                        'bandwidth_min': bw_min,
                        'bandwidth_max': bw_max,
                        'fading_std_db': fading
                    }
                    best_results = results
    
    print(f"\n🎯 最优参数组合:")
    print("-" * 50)
    if best_params:
        print(f"信道增益常数: {best_params['channel_gain_constant']:.1e}")
        print(f"带宽范围: {best_params['bandwidth_min']/1e6:.0f} - {best_params['bandwidth_max']/1e6:.0f} MHz")
        print(f"衰落标准差: {best_params['fading_std_db']:.1f} dB")
        
        print(f"\n📊 性能指标:")
        print("-" * 30)
        print(f"速率范围: {best_results['min_rate']:.1f} - {best_results['max_rate']:.1f} Mbps")
        print(f"平均速率: {best_results['mean_rate']:.1f} Mbps")
        print(f"标准差: {best_results['std_rate']:.1f} Mbps")
        print(f"目标范围符合性: {best_results['compliance']:.1f}%")
        print(f"样本数: {best_results['sample_count']}")
        print(f"综合评分: {best_results['score']:.2f}")
        
        return best_params, best_results
    else:
        print("未找到合适的参数组合")
        return None, None

def apply_optimal_parameters(params):
    """应用最优参数到配置文件"""
    if params:
        print(f"\n🔧 应用最优参数到配置...")
        
        config.physical.channel_gain_constant = params['channel_gain_constant']
        config.network.bandwidth_min = params['bandwidth_min']
        config.network.bandwidth_max = params['bandwidth_max']
        config.physical.fading_std_db = params['fading_std_db']
        
        print("✅ 参数已更新到配置中")
        
        # 验证应用效果
        print(f"\n🧪 验证应用效果...")
        results = test_parameter_combination(
            params['channel_gain_constant'],
            params['bandwidth_min'],
            params['bandwidth_max'],
            params['fading_std_db'],
            num_samples=4000
        )
        
        if results:
            print(f"验证结果:")
            print(f"  速率范围: {results['min_rate']:.1f} - {results['max_rate']:.1f} Mbps")
            print(f"  平均速率: {results['mean_rate']:.1f} Mbps")
            print(f"  目标范围符合性: {results['compliance']:.1f}%")
            
            if results['compliance'] > 70:
                print("✅ 参数优化成功！")
            elif results['compliance'] > 50:
                print("✅ 参数优化良好")
            else:
                print("⚠️ 参数优化效果有限")
        
        return results
    else:
        print("❌ 无有效参数可应用")
        return None

def generate_config_update_code(params):
    """生成配置更新代码"""
    if params:
        print(f"\n📝 配置文件更新代码:")
        print("-" * 50)
        print("# 在 config.py 中更新以下参数:")
        print(f"channel_gain_constant: float = {params['channel_gain_constant']:.1e}")
        print(f"bandwidth_min: float = {params['bandwidth_min']:.0f}e6  # {params['bandwidth_min']/1e6:.0f} MHz")
        print(f"bandwidth_max: float = {params['bandwidth_max']:.0f}e6  # {params['bandwidth_max']/1e6:.0f} MHz")
        print(f"fading_std_db: float = {params['fading_std_db']:.1f}")

def main():
    """主优化函数"""
    print("香农公式参数优化工具")
    print("目标: 使通信速率主要分布在 [52, 108] Mbps 范围内")
    print("=" * 80)
    
    # 执行参数优化
    best_params, best_results = optimize_parameters()
    
    if best_params:
        # 应用最优参数
        final_results = apply_optimal_parameters(best_params)
        
        # 生成配置更新代码
        generate_config_update_code(best_params)
        
        print(f"\n" + "="*80)
        print("参数优化完成！")
        print("="*80)
        print("新的香农公式通信模型已优化为目标速率范围 [52, 108] Mbps")
        
    else:
        print("参数优化失败，请检查搜索范围和约束条件")

if __name__ == "__main__":
    main()
