#!/usr/bin/env python3
"""
优化修正后的香农公式参数以提高[52,108]Mbps范围符合性
"""

import numpy as np
from config import config
from environment import EdgeComputingEnvironment

def test_parameter_combination(channel_gain_constant, bandwidth_min, bandwidth_max, 
                              fading_std, num_samples=2000):
    """测试参数组合的效果"""
    
    # 临时修改配置
    original_gain = config.physical.channel_gain_constant
    original_bw_min = config.network.bandwidth_min
    original_bw_max = config.network.bandwidth_max
    original_fading = config.physical.fading_std_db
    
    config.physical.channel_gain_constant = channel_gain_constant
    config.network.bandwidth_min = bandwidth_min
    config.network.bandwidth_max = bandwidth_max
    config.physical.fading_std_db = fading_std
    
    try:
        env = EdgeComputingEnvironment(num_devices=4, num_servers=4, random_seed=42)
        
        rates = []
        for _ in range(num_samples // 16):  # 每次更新网络条件产生16个速率
            env._update_network_conditions()
            for condition in env.network_conditions:
                for rate in condition['server_rates']:
                    if rate > 0:
                        rates.append(rate / 1e6)  # 转换为Mbps
        
        if rates:
            rates = np.array(rates)
            
            # 计算统计信息
            min_rate = np.min(rates)
            max_rate = np.max(rates)
            mean_rate = np.mean(rates)
            std_rate = np.std(rates)
            
            # 计算目标范围符合性
            in_range = np.sum((rates >= 52) & (rates <= 108))
            compliance = in_range / len(rates) * 100
            
            # 计算分布质量评分
            target_center = 80  # 目标中心点
            center_deviation = abs(mean_rate - target_center)
            
            # 综合评分 (优先考虑符合性)
            score = compliance * 2 - center_deviation * 0.3 - std_rate * 0.1
            
            return {
                'rates': rates,
                'min_rate': min_rate,
                'max_rate': max_rate,
                'mean_rate': mean_rate,
                'std_rate': std_rate,
                'compliance': compliance,
                'score': score,
                'sample_count': len(rates)
            }
        else:
            return None
            
    finally:
        # 恢复原始配置
        config.physical.channel_gain_constant = original_gain
        config.network.bandwidth_min = original_bw_min
        config.network.bandwidth_max = original_bw_max
        config.physical.fading_std_db = original_fading

def optimize_for_p_tx_range():
    """针对p_tx范围优化参数"""
    print("="*80)
    print("针对p_tx范围优化香农公式参数")
    print(f"设备传输功率: {config.device.p_tx_min:.3f} - {config.device.p_tx_max:.3f} W")
    print("目标: 提高 [52, 108] Mbps 范围符合性")
    print("="*80)
    
    best_score = -1000
    best_params = None
    best_results = None
    
    # 针对较低传输功率调整参数搜索范围
    channel_gains = [2e-6, 2.5e-6, 3e-6, 3.5e-6, 4e-6, 4.5e-6, 5e-6]
    bandwidth_ranges = [
        (10e6, 18e6),   # 10-18 MHz (较窄带宽)
        (12e6, 20e6),   # 12-20 MHz
        (15e6, 25e6),   # 15-25 MHz (当前)
        (18e6, 28e6),   # 18-28 MHz
        (20e6, 30e6),   # 20-30 MHz (较宽带宽)
    ]
    fading_stds = [3.0, 4.0, 5.0, 6.0, 7.0]
    
    print("正在搜索最优参数组合...")
    print("参数组合数:", len(channel_gains) * len(bandwidth_ranges) * len(fading_stds))
    print()
    
    test_count = 0
    total_tests = len(channel_gains) * len(bandwidth_ranges) * len(fading_stds)
    
    for gain in channel_gains:
        for bw_min, bw_max in bandwidth_ranges:
            for fading in fading_stds:
                test_count += 1
                
                if test_count % 10 == 0:
                    print(f"进度: {test_count}/{total_tests} ({test_count/total_tests*100:.1f}%)")
                
                results = test_parameter_combination(gain, bw_min, bw_max, fading)
                
                if results and results['score'] > best_score:
                    best_score = results['score']
                    best_params = {
                        'channel_gain_constant': gain,
                        'bandwidth_min': bw_min,
                        'bandwidth_max': bw_max,
                        'fading_std_db': fading
                    }
                    best_results = results
    
    print(f"\n🎯 最优参数组合:")
    print("-" * 50)
    if best_params:
        print(f"信道增益常数: {best_params['channel_gain_constant']:.1e}")
        print(f"带宽范围: {best_params['bandwidth_min']/1e6:.0f} - {best_params['bandwidth_max']/1e6:.0f} MHz")
        print(f"衰落标准差: {best_params['fading_std_db']:.1f} dB")
        
        print(f"\n📊 性能指标:")
        print("-" * 30)
        print(f"速率范围: {best_results['min_rate']:.1f} - {best_results['max_rate']:.1f} Mbps")
        print(f"平均速率: {best_results['mean_rate']:.1f} Mbps")
        print(f"标准差: {best_results['std_rate']:.1f} Mbps")
        print(f"目标范围符合性: {best_results['compliance']:.1f}%")
        print(f"样本数: {best_results['sample_count']}")
        print(f"综合评分: {best_results['score']:.2f}")
        
        return best_params, best_results
    else:
        print("未找到合适的参数组合")
        return None, None

def apply_and_verify_parameters(params):
    """应用并验证最优参数"""
    if params:
        print(f"\n🔧 应用最优参数...")
        
        # 应用参数
        config.physical.channel_gain_constant = params['channel_gain_constant']
        config.network.bandwidth_min = params['bandwidth_min']
        config.network.bandwidth_max = params['bandwidth_max']
        config.physical.fading_std_db = params['fading_std_db']
        
        print("✅ 参数已更新")
        
        # 大规模验证
        print(f"\n🧪 大规模验证 (10000样本)...")
        results = test_parameter_combination(
            params['channel_gain_constant'],
            params['bandwidth_min'],
            params['bandwidth_max'],
            params['fading_std_db'],
            num_samples=10000
        )
        
        if results:
            print(f"验证结果:")
            print(f"  速率范围: {results['min_rate']:.1f} - {results['max_rate']:.1f} Mbps")
            print(f"  平均速率: {results['mean_rate']:.1f} Mbps")
            print(f"  标准差: {results['std_rate']:.1f} Mbps")
            print(f"  目标范围符合性: {results['compliance']:.1f}%")
            
            # 详细分布分析
            rates = results['rates']
            in_range = np.sum((rates >= 52) & (rates <= 108))
            below_range = np.sum(rates < 52)
            above_range = np.sum(rates > 108)
            
            print(f"\n📈 详细分布:")
            print(f"  [52-108] Mbps: {in_range}/{len(rates)} ({in_range/len(rates)*100:.1f}%)")
            print(f"  < 52 Mbps: {below_range}/{len(rates)} ({below_range/len(rates)*100:.1f}%)")
            print(f"  > 108 Mbps: {above_range}/{len(rates)} ({above_range/len(rates)*100:.1f}%)")
            
            # 分位数分析
            percentiles = [10, 25, 50, 75, 90]
            print(f"\n📊 分位数分析:")
            for p in percentiles:
                value = np.percentile(rates, p)
                print(f"  {p:2d}%分位数: {value:.1f} Mbps")
            
            if results['compliance'] > 75:
                print("✅ 参数优化非常成功！")
            elif results['compliance'] > 60:
                print("✅ 参数优化成功！")
            else:
                print("⚠️ 参数优化效果有限")
        
        return results
    else:
        print("❌ 无有效参数可应用")
        return None

def generate_final_config_code(params):
    """生成最终配置代码"""
    if params:
        print(f"\n📝 最终配置文件更新代码:")
        print("-" * 50)
        print("# 在 config.py 中更新以下参数:")
        print(f"channel_gain_constant: float = {params['channel_gain_constant']:.1e}")
        print(f"bandwidth_min: float = {params['bandwidth_min']:.0f}e6  # {params['bandwidth_min']/1e6:.0f} MHz")
        print(f"bandwidth_max: float = {params['bandwidth_max']:.0f}e6  # {params['bandwidth_max']/1e6:.0f} MHz")
        print(f"fading_std_db: float = {params['fading_std_db']:.1f}")

def main():
    """主优化函数"""
    print("香农公式参数优化 - 针对p_tx范围")
    print("=" * 80)
    
    # 显示当前p_tx范围
    print(f"当前设备传输功率范围: {config.device.p_tx_min:.3f} - {config.device.p_tx_max:.3f} W")
    print(f"对应功率范围: {10*np.log10(config.device.p_tx_min*1000):.1f} - {10*np.log10(config.device.p_tx_max*1000):.1f} dBm")
    print()
    
    # 执行参数优化
    best_params, best_results = optimize_for_p_tx_range()
    
    if best_params:
        # 应用并验证最优参数
        final_results = apply_and_verify_parameters(best_params)
        
        # 生成配置更新代码
        generate_final_config_code(best_params)
        
        print(f"\n" + "="*80)
        print("🎉 针对p_tx范围的参数优化完成！")
        print("="*80)
        print("香农公式现在使用:")
        print("• 正确的设备传输功率 p_tx (0.093-0.115 W)")
        print("• 优化的信道增益常数")
        print("• 调整的带宽范围")
        print("• 目标速率范围 [52, 108] Mbps")
        
        if final_results:
            print(f"• 最终符合性: {final_results['compliance']:.1f}%")
        
    else:
        print("参数优化失败，请检查搜索范围")

if __name__ == "__main__":
    main()
