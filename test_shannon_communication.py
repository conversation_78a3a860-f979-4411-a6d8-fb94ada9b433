#!/usr/bin/env python3
"""
测试基于香农公式的通信模型
Test Shannon Capacity-based Communication Model
"""

import numpy as np
import matplotlib.pyplot as plt
from config import config
from models import LocalDevice, EdgeServer
from environment import EdgeComputingEnvironment

def test_shannon_capacity():
    """测试香农容量计算"""
    print("="*80)
    print("香农容量通信模型测试")
    print("="*80)
    
    print("\n📡 1. 模型参数配置")
    print("-" * 50)
    print(f"信道带宽: {config.network.bandwidth/1e6:.1f} MHz")
    print(f"噪声功率: {config.network.noise_power_dbm} dBm")
    print(f"发射功率范围: {config.network.tx_power_dbm_min}-{config.network.tx_power_dbm_max} dBm")
    print(f"路径损耗指数: {config.physical.path_loss_exponent}")
    print(f"参考距离: {config.physical.reference_distance} m")
    print(f"参考距离路径损耗: {config.physical.path_loss_at_reference} dB")
    print(f"阴影衰落标准差: {config.physical.shadowing_std} dB")
    print(f"最小SNR: {config.physical.min_snr_db} dB")
    
    # 创建环境进行测试
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    print("\n📊 2. 不同距离下的容量测试")
    print("-" * 50)
    
    # 测试不同距离下的容量
    test_distances = [1, 10, 50, 100, 200, 500, 800, 1000, 1200, 1500]
    
    print("距离(m) | 平均容量(Mbps) | 最小容量(Mbps) | 最大容量(Mbps) | 成功率(%)")
    print("-" * 75)
    
    for distance in test_distances:
        capacities = []
        success_count = 0
        
        # 每个距离测试100次（考虑随机性）
        for _ in range(100):
            # 固定位置进行测试
            device_x, device_y = 0, 0
            server_x, server_y = distance, 0
            
            capacity = env._calculate_shannon_capacity(
                device_x, device_y, server_x, server_y
            )
            
            if capacity > 0:
                capacities.append(capacity)
                success_count += 1
        
        if capacities:
            avg_capacity = np.mean(capacities) / 1e6  # 转换为Mbps
            min_capacity = np.min(capacities) / 1e6
            max_capacity = np.max(capacities) / 1e6
        else:
            avg_capacity = min_capacity = max_capacity = 0
        
        success_rate = success_count
        
        print(f"{distance:6d}  |    {avg_capacity:8.2f}    |    {min_capacity:8.2f}    |    {max_capacity:8.2f}    |   {success_rate:3d}")

def test_snr_calculation():
    """测试SNR计算过程"""
    print("\n" + "="*80)
    print("SNR计算过程详细分析")
    print("="*80)
    
    # 固定参数进行详细分析
    distance = 100  # 100米
    tx_power_dbm = 25  # 25 dBm (316 mW)
    
    print(f"\n🔧 测试参数:")
    print(f"距离: {distance} m")
    print(f"发射功率: {tx_power_dbm} dBm")
    print(f"噪声功率: {config.network.noise_power_dbm} dBm")
    
    print(f"\n📐 计算过程:")
    
    # 1. 路径损耗计算
    path_loss_db = (
        config.physical.path_loss_at_reference +
        10 * config.physical.path_loss_exponent * 
        np.log10(distance / config.physical.reference_distance)
    )
    print(f"1. 路径损耗 = {config.physical.path_loss_at_reference} + 10×{config.physical.path_loss_exponent}×log10({distance}/{config.physical.reference_distance})")
    print(f"   路径损耗 = {path_loss_db:.2f} dB")
    
    # 2. 阴影衰落（示例）
    shadowing_db = 5.0  # 示例值
    total_path_loss_db = path_loss_db + shadowing_db
    print(f"2. 总路径损耗 = {path_loss_db:.2f} + {shadowing_db:.2f} = {total_path_loss_db:.2f} dB")
    
    # 3. 接收功率
    rx_power_dbm = tx_power_dbm - total_path_loss_db
    print(f"3. 接收功率 = {tx_power_dbm} - {total_path_loss_db:.2f} = {rx_power_dbm:.2f} dBm")
    
    # 4. SNR
    snr_db = rx_power_dbm - config.network.noise_power_dbm
    print(f"4. SNR = {rx_power_dbm:.2f} - ({config.network.noise_power_dbm}) = {snr_db:.2f} dB")
    
    # 5. 线性SNR
    snr_linear = 10 ** (snr_db / 10)
    print(f"5. SNR(线性) = 10^({snr_db:.2f}/10) = {snr_linear:.2f}")
    
    # 6. 香农容量
    capacity_bps = config.network.bandwidth * np.log2(1 + snr_linear)
    capacity_mbps = capacity_bps / 1e6
    print(f"6. 容量 = {config.network.bandwidth/1e6:.1f}MHz × log2(1 + {snr_linear:.2f}) = {capacity_mbps:.2f} Mbps")

def compare_models():
    """比较新旧通信模型"""
    print("\n" + "="*80)
    print("新旧通信模型对比")
    print("="*80)
    
    print("\n📊 模型特征对比:")
    print("-" * 50)
    
    print("旧模型（速率因子）:")
    print("• 公式: Rate = base_rate × exp(-decay × distance)")
    print("• 参数: 基础速率、衰减因子、最小速率比例")
    print("• 特点: 简单、确定性（除基础速率随机）")
    print("• 物理意义: 有限")
    
    print("\n新模型（香农容量）:")
    print("• 公式: C = B × log2(1 + SNR)")
    print("• 参数: 带宽、发射功率、路径损耗、噪声功率")
    print("• 特点: 复杂、随机性（功率、阴影衰落）")
    print("• 物理意义: 强（基于信息论）")
    
    print("\n🔬 性能对比测试:")
    print("-" * 50)
    
    # 创建环境
    env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
    
    # 更新网络条件
    env._update_network_conditions()
    
    print("设备到服务器的传输速率:")
    for i, condition in enumerate(env.network_conditions):
        device_pos = condition['device_position']
        print(f"\n设备 {i} (位置: {device_pos[0]:.1f}, {device_pos[1]:.1f}):")
        
        for j, rate in enumerate(condition['server_rates']):
            server_pos = env.servers[j].get_position()
            distance = np.sqrt((device_pos[0] - server_pos[0])**2 + 
                             (device_pos[1] - server_pos[1])**2)
            
            print(f"  -> 服务器 {j}: {rate/1e6:.2f} Mbps (距离: {distance:.1f}m)")

def analyze_model_properties():
    """分析新模型的特性"""
    print("\n" + "="*80)
    print("香农容量模型特性分析")
    print("="*80)
    
    print("\n🎯 1. 模型优势:")
    print("-" * 30)
    print("✓ 理论基础: 基于信息论的香农容量公式")
    print("✓ 物理真实: 考虑发射功率、路径损耗、噪声")
    print("✓ 随机性: 阴影衰落模拟真实信道变化")
    print("✓ 参数丰富: 可调节多个物理参数")
    print("✓ 约束合理: SNR阈值和距离限制")
    
    print("\n⚠️ 2. 注意事项:")
    print("-" * 30)
    print("• 计算复杂度增加")
    print("• 参数调节需要无线通信知识")
    print("• 随机性可能影响训练稳定性")
    print("• 需要合理设置SNR阈值")
    
    print("\n🔧 3. 参数建议:")
    print("-" * 30)
    print(f"• 带宽: {config.network.bandwidth/1e6:.1f} MHz (典型WiFi/LTE)")
    print(f"• 发射功率: {config.network.tx_power_dbm_min}-{config.network.tx_power_dbm_max} dBm (移动设备典型)")
    print(f"• 路径损耗指数: {config.physical.path_loss_exponent} (自由空间)")
    print(f"• 阴影衰落: {config.physical.shadowing_std} dB (典型室外环境)")
    print(f"• 最小SNR: {config.physical.min_snr_db} dB (保证基本通信)")

def main():
    """主测试函数"""
    print("基于香农公式的通信模型测试")
    print("=" * 80)
    
    test_shannon_capacity()
    test_snr_calculation()
    compare_models()
    analyze_model_properties()
    
    print("\n" + "="*80)
    print("测试完成")
    print("="*80)
    print("新的香农容量模型已成功实现并集成到系统中。")
    print("模型提供了更强的物理意义和更丰富的参数控制。")

if __name__ == "__main__":
    main()
