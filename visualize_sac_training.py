#!/usr/bin/env python3
"""
SAC训练统计数据可视化脚本
读取sac_training_stats.json文件并创建训练过程的可视化图表
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter1d
import os

def load_training_stats(file_path):
    """加载训练统计数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return None
    except json.JSONDecodeError:
        print(f"错误: 无法解析JSON文件 {file_path}")
        return None

def process_data(data):
    """处理训练数据"""
    processed_data = {}

    # 处理奖励数据 - 除以4后计算平均值
    if 'episode_rewards' in data:
        rewards = np.array(data['episode_rewards'])
        processed_data['rewards_raw'] = rewards / 4  # 原始数据除以4
        processed_data['rewards'] = rewards / 4      # 用于平滑处理的数据

    # 处理能耗数据 - 除以4后计算平均值
    if 'episode_energies' in data:
        energies = np.array(data['episode_energies'])
        processed_data['energies_raw'] = energies / 4  # 原始数据除以4
        processed_data['energies'] = energies / 4      # 用于平滑处理的数据

    # 处理延迟数据 - 除以4后计算平均值
    if 'episode_delays' in data:
        delays = np.array(data['episode_delays'])
        processed_data['delays_raw'] = delays / 4  # 原始数据除以4
        processed_data['delays'] = delays / 4      # 用于平滑处理的数据

    # 处理电池百分比数据 - 直接计算平均值
    if 'avg_battery_levels' in data:
        battery_levels = np.array(data['avg_battery_levels'])
        processed_data['battery_percentages_raw'] = battery_levels * 100  # 原始数据
        processed_data['battery_percentages'] = battery_levels * 100      # 用于平滑处理的数据

    return processed_data

def smooth_curve(data, sigma=2):
    """使用高斯滤波器平滑曲线"""
    return gaussian_filter1d(data, sigma=sigma)

def create_visualization(processed_data):
    """创建可视化图表"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建2x2子图布局
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 定义颜色和样式
    smooth_color = '#1f77b4'  # 蓝色 - 平滑曲线
    raw_color = '#d62728'     # 红色 - 原始数据
    line_width = 2.5
    raw_alpha = 0.6
    smooth_alpha = 0.9
    
    # 1. 奖励变化图
    if 'rewards' in processed_data and 'rewards_raw' in processed_data:
        episodes = range(1, len(processed_data['rewards']) + 1)
        smoothed_rewards = smooth_curve(processed_data['rewards'])
        raw_rewards = processed_data['rewards_raw']

        # 绘制原始数据（红色）
        axes[0, 0].plot(episodes, raw_rewards, color=raw_color, linewidth=1.5, alpha=raw_alpha, label='原始数据')
        # 绘制平滑数据（蓝色）
        axes[0, 0].plot(episodes, smoothed_rewards, color=smooth_color, linewidth=line_width, alpha=smooth_alpha, label='平滑曲线')

        axes[0, 0].set_title('平均奖励变化', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('Episode', fontsize=12)
        axes[0, 0].set_ylabel('平均奖励', fontsize=12)
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(labelsize=10)
        axes[0, 0].legend(fontsize=10)
    
    # 2. 能耗变化图
    if 'energies' in processed_data and 'energies_raw' in processed_data:
        episodes = range(1, len(processed_data['energies']) + 1)
        smoothed_energies = smooth_curve(processed_data['energies'])
        raw_energies = processed_data['energies_raw']

        # 绘制原始数据（红色）
        axes[0, 1].plot(episodes, raw_energies, color=raw_color, linewidth=1.5, alpha=raw_alpha, label='原始数据')
        # 绘制平滑数据（蓝色）
        axes[0, 1].plot(episodes, smoothed_energies, color=smooth_color, linewidth=line_width, alpha=smooth_alpha, label='平滑曲线')

        axes[0, 1].set_title('平均能耗变化', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Episode', fontsize=12)
        axes[0, 1].set_ylabel('平均能耗 (J)', fontsize=12)
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].tick_params(labelsize=10)
        axes[0, 1].legend(fontsize=10)
    
    # 3. 延迟变化图
    if 'delays' in processed_data and 'delays_raw' in processed_data:
        episodes = range(1, len(processed_data['delays']) + 1)
        smoothed_delays = smooth_curve(processed_data['delays'])
        raw_delays = processed_data['delays_raw']

        # 绘制原始数据（红色）
        axes[1, 0].plot(episodes, raw_delays, color=raw_color, linewidth=1.5, alpha=raw_alpha, label='原始数据')
        # 绘制平滑数据（蓝色）
        axes[1, 0].plot(episodes, smoothed_delays, color=smooth_color, linewidth=line_width, alpha=smooth_alpha, label='平滑曲线')

        axes[1, 0].set_title('平均延迟变化', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('Episode', fontsize=12)
        axes[1, 0].set_ylabel('平均延迟 (s)', fontsize=12)
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].tick_params(labelsize=10)
        axes[1, 0].legend(fontsize=10)
    
    # 4. 电池百分比变化图
    if 'battery_percentages' in processed_data and 'battery_percentages_raw' in processed_data:
        episodes = range(1, len(processed_data['battery_percentages']) + 1)
        smoothed_battery = smooth_curve(processed_data['battery_percentages'])
        raw_battery = processed_data['battery_percentages_raw']

        # 绘制原始数据（红色）
        axes[1, 1].plot(episodes, raw_battery, color=raw_color, linewidth=1.5, alpha=raw_alpha, label='原始数据')
        # 绘制平滑数据（蓝色）
        axes[1, 1].plot(episodes, smoothed_battery, color=smooth_color, linewidth=line_width, alpha=smooth_alpha, label='平滑曲线')

        axes[1, 1].set_title('平均电池百分比变化', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Episode', fontsize=12)
        axes[1, 1].set_ylabel('平均电池百分比 (%)', fontsize=12)
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].tick_params(labelsize=10)
        axes[1, 1].legend(fontsize=10)
        axes[1, 1].set_ylim(0, 100)  # 设置y轴范围为0-100%
    
    # 调整子图间距
    plt.tight_layout()
    
    return fig

def print_statistics(processed_data):
    """打印统计信息"""
    print("\n=== SAC训练统计信息 ===")
    
    for key, values in processed_data.items():
        if key == 'rewards':
            name = "奖励"
            unit = ""
        elif key == 'energies':
            name = "能耗"
            unit = " J"
        elif key == 'delays':
            name = "延迟"
            unit = " s"
        elif key == 'battery_percentages':
            name = "电池百分比"
            unit = " %"
        else:
            continue
            
        print(f"\n{name}统计:")
        print(f"  总Episode数: {len(values)}")
        print(f"  最终值: {values[-1]:.4f}{unit}")
        print(f"  最大值: {np.max(values):.4f}{unit}")
        print(f"  最小值: {np.min(values):.4f}{unit}")
        print(f"  平均值: {np.mean(values):.4f}{unit}")
        print(f"  标准差: {np.std(values):.4f}{unit}")

def main():
    """主函数"""
    # 文件路径
    stats_file = "results/sac_training_stats.json"
    
    # 检查文件是否存在
    if not os.path.exists(stats_file):
        print(f"错误: 找不到文件 {stats_file}")
        print("请确保文件路径正确，并且文件存在。")
        return
    
    # 加载数据
    print("正在加载训练统计数据...")
    data = load_training_stats(stats_file)
    if data is None:
        return
    
    # 处理数据
    print("正在处理数据...")
    processed_data = process_data(data)
    
    if not processed_data:
        print("错误: 没有找到有效的训练数据")
        return
    
    # 打印统计信息
    print_statistics(processed_data)
    
    # 创建可视化
    print("正在创建可视化图表...")
    fig = create_visualization(processed_data)
    
    # 保存图表
    output_file = "results/sac_training_visualization.png"
    fig.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"图表已保存到: {output_file}")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    main()
