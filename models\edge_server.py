"""Edge Server Model for Heterogeneous Multi-Edge Server DNN Inference Optimization
Enhanced server model with queuing, resource competition and physical positioning
"""

import numpy as np
import time
from collections import deque
from typing import Dict, Any, List, Optional, Tuple
from config import config

class EdgeServer:
    """
    Enhanced edge server model with queuing, resource competition and physical positioning
    Based on laptop 4060 specifications with realistic resource constraints
    """

    def __init__(self, server_id: int, position: tuple = None):
        """
        Initialize edge server with queuing and resource management

        Args:
            server_id: Unique server identifier (0-3)
            position: Physical position (x, y) in meters, if None will be randomly assigned
        """
        self.server_id = server_id
        self.config = config.server

        # Physical position
        if position is not None:
            self.x, self.y = position
        else:
            self.x = np.random.uniform(0, config.physical.area_width)
            self.y = np.random.uniform(0, config.physical.area_height)
        
        # Server specifications
        self.f_server = np.random.uniform(
            self.config.f_server_min,
            self.config.f_server_max
        )  # Hz
        self.g_server = self.config.g_server  # FLOPS per cycle
        self.server_type = self.config.server_type

        # Queue management
        self.task_queue = deque()  # FIFO queue for waiting tasks
        self.max_queue_size = self.config.max_queue_size
        self.active_tasks = []  # List of currently processing tasks
        self.service_rate = self.config.service_rate  # tasks per second

        # Resource competition parameters
        self.enable_resource_competition = self.config.enable_resource_competition
        self.processing_overhead = self.config.processing_overhead

        # Performance tracking
        self.task_count_history = []
        self.queue_length_history = []
        self.waiting_time_history = []

        # Timing for queue simulation
        self.last_update_time = time.time()
        
    def get_state(self) -> np.ndarray:
        """
        Get current server state for RL environment

        Returns:
            State vector: [queue_utilization, processing_utilization, x_normalized, y_normalized]
        """
        # Update queue state before getting state
        self._update_queue_state()

        # Queue utilization (0-1)
        queue_utilization = len(self.task_queue) / self.max_queue_size

        # Processing utilization (0-1)
        max_concurrent_tasks = 4  # Realistic limit for concurrent processing
        processing_utilization = min(len(self.active_tasks) / max_concurrent_tasks, 1.0)

        # Normalized position
        x_normalized = self.x / config.physical.area_width
        y_normalized = self.y / config.physical.area_height

        return np.array([queue_utilization, processing_utilization,
                        x_normalized, y_normalized], dtype=np.float32)
    
    def _update_queue_state(self):
        """Update queue state by processing completed tasks"""
        current_time = time.time()
        time_elapsed = current_time - self.last_update_time

        # Simulate task completion based on service rate
        if self.active_tasks and time_elapsed > 0:
            # Simple simulation: complete tasks based on service rate
            tasks_to_complete = min(
                len(self.active_tasks),
                int(time_elapsed * self.service_rate)
            )

            # Remove completed tasks
            for _ in range(tasks_to_complete):
                if self.active_tasks:
                    self.active_tasks.pop(0)

            # Move tasks from queue to active processing
            while (self.task_queue and
                   len(self.active_tasks) < 4):  # Max 4 concurrent tasks
                task = self.task_queue.popleft()
                self.active_tasks.append(task)

        self.last_update_time = current_time

    def can_accept_task(self, required_flops: float, estimated_time: float = None) -> bool:
        """
        Check if server can accept a new task based on queue capacity

        Args:
            required_flops: Required FLOPs for the task
            estimated_time: Estimated execution time in seconds (if None, calculated)

        Returns:
            True if task can be accepted (queue not full), False otherwise
        """
        self._update_queue_state()
        total_tasks = len(self.task_queue) + len(self.active_tasks)
        return total_tasks < self.max_queue_size
    
    def allocate_task(self, task_id: int, required_flops: float, device_id: int,
                     estimated_time: float = None) -> bool:
        """
        Allocate computational resources for a task with queuing

        Args:
            task_id: Unique task identifier
            required_flops: Required FLOPs for the task
            device_id: ID of the device requesting the task
            estimated_time: Estimated execution time in seconds

        Returns:
            True if task was accepted, False if queue is full
        """
        if not self.can_accept_task(required_flops, estimated_time):
            return False

        if estimated_time is None:
            estimated_time = self.calculate_processing_time(required_flops)

        # Add resource competition overhead
        if self.enable_resource_competition and len(self.active_tasks) > 0:
            estimated_time *= (1 + self.processing_overhead)

        # Create task info
        task_info = {
            'task_id': task_id,
            'device_id': device_id,
            'required_flops': required_flops,
            'estimated_time': estimated_time,
            'arrival_time': time.time(),
            'start_time': None
        }

        # Add to queue or active processing
        if len(self.active_tasks) < 4:  # Max 4 concurrent tasks
            task_info['start_time'] = time.time()
            self.active_tasks.append(task_info)
        else:
            self.task_queue.append(task_info)

        return True
    
    def deallocate_task(self, task_id: int) -> bool:
        """
        Deallocate computational resources for a completed task (simplified)
        
        Args:
            task_id: Task identifier to deallocate
            
        Returns:
            True if task was deallocated successfully, False if task not found
        """
        for i, task in enumerate(self.active_tasks):
            if task['task_id'] == task_id:
                # Remove task from active list
                self.active_tasks.pop(i)
                return True
        return False
    
    def calculate_processing_time(self, flops: float) -> float:
        """
        Calculate processing time for given FLOPs

        Args:
            flops: Number of floating point operations

        Returns:
            Processing time in seconds
        """
        # T_edge = C_edge / (f_server * g_server)
        base_time = flops / (self.f_server * self.g_server)

        # Add resource competition overhead if enabled
        if self.enable_resource_competition and len(self.active_tasks) > 0:
            base_time *= (1 + self.processing_overhead)

        return base_time

    def calculate_queuing_delay(self, task_id: int = None) -> float:
        """
        Calculate expected queuing delay for a new task or existing task

        Args:
            task_id: If provided, calculate delay for existing task, otherwise for new task

        Returns:
            Expected queuing delay in seconds
        """
        self._update_queue_state()

        if task_id is not None:
            # Find task in queue or active tasks
            for task in self.task_queue:
                if task['task_id'] == task_id:
                    # Task is in queue - calculate waiting time
                    queue_position = list(self.task_queue).index(task)
                    # Estimate delay based on queue position and service rate
                    return queue_position / self.service_rate

            for task in self.active_tasks:
                if task['task_id'] == task_id:
                    # Task is being processed - no additional queuing delay
                    return 0.0

            # Task not found
            return 0.0
        else:
            # Calculate delay for a new task
            queue_length = len(self.task_queue)
            active_tasks = len(self.active_tasks)

            if active_tasks < 4:  # Can start immediately
                return 0.0
            else:
                # Must wait in queue
                return queue_length / self.service_rate
    
    def get_position(self) -> Tuple[float, float]:
        """Get server physical position"""
        return (self.x, self.y)

    def calculate_distance_to(self, target_x: float, target_y: float) -> float:
        """Calculate Euclidean distance to target position"""
        return np.sqrt((self.x - target_x)**2 + (self.y - target_y)**2)

    def get_current_task_count(self) -> int:
        """
        Get current number of active tasks

        Returns:
            Number of active tasks
        """
        self._update_queue_state()
        return len(self.active_tasks)

    def get_queue_length(self) -> int:
        """
        Get current queue length

        Returns:
            Number of tasks waiting in queue
        """
        self._update_queue_state()
        return len(self.task_queue)

    def get_total_load(self) -> int:
        """
        Get total server load (active + queued tasks)

        Returns:
            Total number of tasks
        """
        self._update_queue_state()
        return len(self.active_tasks) + len(self.task_queue)
    
    def update_history(self):
        """Update server state history for tracking"""
        self._update_queue_state()
        self.task_count_history.append(len(self.active_tasks))
        self.queue_length_history.append(len(self.task_queue))
    
    def reset(self, position: tuple = None):
        """Reset server to initial state"""
        # Reset physical position
        if position is not None:
            self.x, self.y = position
        else:
            self.x = np.random.uniform(0, config.physical.area_width)
            self.y = np.random.uniform(0, config.physical.area_height)

        # Reset queues and tasks
        self.task_queue.clear()
        self.active_tasks.clear()
        self.task_count_history.clear()
        self.queue_length_history.clear()
        self.waiting_time_history.clear()
        self.last_update_time = time.time()
    
    def get_info(self) -> Dict[str, Any]:
        """Get comprehensive server information"""
        self._update_queue_state()
        return {
            'server_id': self.server_id,
            'position': (self.x, self.y),
            'frequency_hz': self.f_server,
            'frequency_ghz': self.f_server / 1e9,
            'active_tasks': len(self.active_tasks),
            'queued_tasks': len(self.task_queue),
            'total_load': len(self.active_tasks) + len(self.task_queue),
            'queue_utilization': len(self.task_queue) / self.max_queue_size,
            'server_type': self.server_type,
            'max_queue_size': self.max_queue_size,
            'service_rate': self.service_rate
        }
    
    def get_task_info(self, task_id: int) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task information dictionary or None if task not found
        """
        for task in self.active_tasks:
            if task['task_id'] == task_id:
                return task.copy()
        return None
    
    def get_all_tasks_info(self) -> List[Dict[str, Any]]:
        """Get information about all active tasks"""
        return [task.copy() for task in self.active_tasks]
    
    def __str__(self) -> str:
        """String representation of server state"""
        info = self.get_info()
        return (f"Server {info['server_id']}: "
                f"Tasks={info['active_tasks']}, "
                f"Freq={info['frequency_ghz']:.2f}GHz")
