#!/usr/bin/env python3
"""
最终验证正确的香农公式实现
Final Validation of Correct Shannon Formula Implementation
"""

import numpy as np
from config import config
from environment import EdgeComputingEnvironment

def validate_shannon_formula():
    """验证香农公式实现"""
    print("="*80)
    print("最终香农公式验证")
    print("Rn = bn × log2(1 + (pn × hn) / (bn × N0))")
    print("="*80)
    
    print("\n📋 1. 最终参数配置")
    print("-" * 50)
    print("香农公式参数:")
    print(f"• pn (设备传输功率): {config.device.p_tx_min:.3f} - {config.device.p_tx_max:.3f} W")
    print(f"  对应功率: {10*np.log10(config.device.p_tx_min*1000):.1f} - {10*np.log10(config.device.p_tx_max*1000):.1f} dBm")
    print(f"• bn (带宽): {config.network.bandwidth_min/1e6:.0f} - {config.network.bandwidth_max/1e6:.0f} MHz")
    print(f"• N0 (噪声功率谱密度): {config.network.noise_psd_dbm_hz} dBm/Hz")
    print(f"• hn (信道增益常数): {config.physical.channel_gain_constant:.1e}")
    print(f"• 路径损耗指数: {config.physical.path_loss_exponent}")
    print(f"• 衰落标准差: {config.physical.fading_std_db} dB")
    print(f"• 目标速率范围: {config.network.rate_min/1e6:.0f} - {config.network.rate_max/1e6:.0f} Mbps")

def comprehensive_rate_test():
    """综合速率测试"""
    print("\n📊 2. 综合速率分布测试")
    print("-" * 50)
    
    env = EdgeComputingEnvironment(num_devices=4, num_servers=4, random_seed=42)
    
    all_rates = []
    print("正在收集大规模速率样本...")
    
    # 收集更多样本以获得准确统计
    for _ in range(1000):
        env._update_network_conditions()
        for condition in env.network_conditions:
            for rate in condition['server_rates']:
                if rate > 0:
                    all_rates.append(rate / 1e6)  # 转换为Mbps
    
    if all_rates:
        rates_array = np.array(all_rates)
        
        print(f"样本数: {len(all_rates)}")
        print(f"速率范围: {np.min(rates_array):.1f} - {np.max(rates_array):.1f} Mbps")
        print(f"平均速率: {np.mean(rates_array):.1f} Mbps")
        print(f"中位数: {np.median(rates_array):.1f} Mbps")
        print(f"标准差: {np.std(rates_array):.1f} Mbps")
        
        # 目标范围详细分析
        in_target_range = np.sum((rates_array >= 52) & (rates_array <= 108))
        below_target = np.sum(rates_array < 52)
        above_target = np.sum(rates_array > 108)
        
        print(f"\n🎯 目标范围 [52, 108] Mbps 详细分析:")
        print(f"• 范围内: {in_target_range:,}/{len(all_rates):,} ({in_target_range/len(all_rates)*100:.1f}%)")
        print(f"• 低于范围: {below_target:,}/{len(all_rates):,} ({below_target/len(all_rates)*100:.1f}%)")
        print(f"• 高于范围: {above_target:,}/{len(all_rates):,} ({above_target/len(all_rates)*100:.1f}%)")
        
        # 分位数分析
        percentiles = [5, 10, 25, 50, 75, 90, 95]
        print(f"\n📈 分位数分析:")
        for p in percentiles:
            value = np.percentile(rates_array, p)
            print(f"• {p:2d}%分位数: {value:.1f} Mbps")
        
        # 评估符合性
        compliance = in_target_range / len(all_rates) * 100
        if compliance >= 75:
            print(f"\n✅ 优秀！目标范围符合性达到 {compliance:.1f}%")
        elif compliance >= 60:
            print(f"\n✅ 良好！目标范围符合性达到 {compliance:.1f}%")
        else:
            print(f"\n⚠️ 需要改进，目标范围符合性仅 {compliance:.1f}%")
        
        return rates_array, compliance
    else:
        print("❌ 未收集到有效速率样本")
        return None, 0

def test_power_distance_relationship():
    """测试功率-距离关系"""
    print("\n⚡ 3. 功率-距离关系测试")
    print("-" * 50)
    
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    # 测试不同功率和距离组合
    power_values = [config.device.p_tx_min, 
                   (config.device.p_tx_min + config.device.p_tx_max) / 2,
                   config.device.p_tx_max]
    distances = [50, 100, 200, 500, 1000]
    
    print("功率(W) | 距离(m) | 平均容量(Mbps) | 容量范围(Mbps)")
    print("-" * 65)
    
    for power in power_values:
        power_dbm = 10 * np.log10(power * 1000)
        for distance in distances:
            capacities = []
            for _ in range(50):
                capacity = env._calculate_shannon_capacity(0, 0, distance, 0, power)
                if capacity > 0:
                    capacities.append(capacity / 1e6)
            
            if capacities:
                avg_capacity = np.mean(capacities)
                min_capacity = np.min(capacities)
                max_capacity = np.max(capacities)
                print(f" {power:.3f}  |  {distance:4d}   |     {avg_capacity:7.1f}      |  {min_capacity:5.1f}-{max_capacity:5.1f}")
            else:
                print(f" {power:.3f}  |  {distance:4d}   |      无通信       |     无通信")

def test_system_performance():
    """测试系统性能"""
    print("\n🚀 4. 系统性能测试")
    print("-" * 50)
    
    try:
        env = EdgeComputingEnvironment(num_devices=3, num_servers=3, random_seed=42)
        
        # 多轮测试
        total_rewards = []
        total_delays = []
        total_energies = []
        
        print("运行多轮性能测试...")
        for episode in range(10):
            states = env.reset()
            
            # 使用随机动作
            actions = [np.random.uniform(-1, 1, env.action_dim) for _ in range(env.num_devices)]
            next_states, rewards, dones, info = env.step(actions)
            
            total_rewards.extend(rewards)
            
            if 'execution_results' in info:
                exec_results = info['execution_results']
                if 'device_delays' in exec_results:
                    total_delays.extend(exec_results['device_delays'])
                if 'device_energies' in exec_results:
                    total_energies.extend(exec_results['device_energies'])
        
        print(f"性能测试结果 (10个episodes):")
        print(f"• 平均奖励: {np.mean(total_rewards):.3f}")
        print(f"• 平均延迟: {np.mean(total_delays):.4f}s")
        print(f"• 平均能耗: {np.mean(total_energies):.4f}J")
        
        # 检查网络条件的合理性
        env._update_network_conditions()
        valid_rates = []
        for condition in env.network_conditions:
            for rate in condition['server_rates']:
                if rate > 0:
                    valid_rates.append(rate / 1e6)
        
        if valid_rates:
            print(f"• 当前网络速率范围: {np.min(valid_rates):.1f} - {np.max(valid_rates):.1f} Mbps")
        
        print("✅ 系统性能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 系统性能测试失败: {e}")
        return False

def generate_summary_report(rates_data, compliance):
    """生成总结报告"""
    print("\n" + "="*80)
    print("🎉 香农公式通信模型重构完成！")
    print("="*80)
    
    print("✅ 重构成果总结:")
    print("-" * 50)
    print("1. 公式实现:")
    print("   • 正确实现香农公式: Rn = bn × log2(1 + (pn × hn) / (bn × N0))")
    print("   • pn 使用设备实际传输功率 p_tx (0.093-0.115 W)")
    print("   • 考虑路径损耗和随机衰落")
    
    print("\n2. 参数优化:")
    print(f"   • 信道增益常数: {config.physical.channel_gain_constant:.1e}")
    print(f"   • 带宽范围: {config.network.bandwidth_min/1e6:.0f}-{config.network.bandwidth_max/1e6:.0f} MHz")
    print(f"   • 衰落标准差: {config.physical.fading_std_db} dB")
    
    print("\n3. 性能指标:")
    if rates_data is not None:
        print(f"   • 速率范围: {np.min(rates_data):.1f} - {np.max(rates_data):.1f} Mbps")
        print(f"   • 平均速率: {np.mean(rates_data):.1f} Mbps")
        print(f"   • 目标范围符合性: {compliance:.1f}%")
    
    print("\n4. 模型特点:")
    print("   • 基于信息论的理论基础")
    print("   • 考虑实际设备功率限制")
    print("   • 包含随机衰落效应")
    print("   • 适合边缘计算场景")
    
    print("\n🚀 系统已准备好用于:")
    print("   • 强化学习算法训练")
    print("   • 边缘计算优化研究")
    print("   • 通信性能评估")
    print("   • 多目标优化实验")

def main():
    """主验证函数"""
    print("正确香农公式通信模型 - 最终验证")
    print("=" * 80)
    
    # 执行验证
    validate_shannon_formula()
    rates_data, compliance = comprehensive_rate_test()
    test_power_distance_relationship()
    system_success = test_system_performance()
    
    # 生成总结报告
    generate_summary_report(rates_data, compliance)
    
    print("\n" + "="*80)
    if system_success and compliance >= 60:
        print("🌟 香农公式通信模型重构成功完成！")
    else:
        print("⚠️ 部分测试需要进一步优化")
    print("="*80)

if __name__ == "__main__":
    main()
