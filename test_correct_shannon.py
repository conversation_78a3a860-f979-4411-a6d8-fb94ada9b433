#!/usr/bin/env python3
"""
测试正确的香农公式实现
Test Correct Shannon Formula Implementation
"""

import numpy as np
import matplotlib.pyplot as plt
from config import config
from environment import EdgeComputingEnvironment

def test_shannon_formula():
    """测试正确的香农公式实现"""
    print("="*80)
    print("正确香农公式测试: Rn = bn × log2(1 + (pn × hn) / (bn × N0))")
    print("="*80)
    
    print("\n📡 1. 模型参数配置")
    print("-" * 50)
    print(f"带宽范围: {config.network.bandwidth_min/1e6:.1f} - {config.network.bandwidth_max/1e6:.1f} MHz")
    print(f"发射功率: {config.network.tx_power_watts} W ({config.network.tx_power_dbm} dBm)")
    print(f"噪声功率谱密度: {config.network.noise_psd_dbm_hz} dBm/Hz")
    print(f"目标速率范围: {config.network.rate_min/1e6:.0f} - {config.network.rate_max/1e6:.0f} Mbps")
    print(f"路径损耗指数: {config.physical.path_loss_exponent}")
    print(f"信道增益常数: {config.physical.channel_gain_constant}")
    print(f"衰落标准差: {config.physical.fading_std_db} dB")
    
    print("\n🔧 2. 手动计算验证")
    print("-" * 50)
    
    # 手动计算示例
    distance = 100  # 100米
    bn = 20e6  # 20 MHz
    pn = 1.0   # 1 W
    
    # 信道增益计算
    hn = config.physical.channel_gain_constant / (distance ** config.physical.path_loss_exponent)
    print(f"距离: {distance} m")
    print(f"带宽: {bn/1e6:.1f} MHz")
    print(f"发射功率: {pn} W")
    print(f"信道增益: hn = {config.physical.channel_gain_constant} / {distance}^{config.physical.path_loss_exponent} = {hn:.2e}")
    
    # 噪声功率计算
    N0_dbm_hz = config.network.noise_psd_dbm_hz
    N0_watts_hz = 10 ** ((N0_dbm_hz - 30) / 10)
    print(f"噪声功率谱密度: {N0_dbm_hz} dBm/Hz = {N0_watts_hz:.2e} W/Hz")
    
    # SNR计算
    snr_linear = (pn * hn) / (bn * N0_watts_hz)
    snr_db = 10 * np.log10(snr_linear)
    print(f"SNR = (pn × hn) / (bn × N0) = ({pn} × {hn:.2e}) / ({bn/1e6:.1f}M × {N0_watts_hz:.2e})")
    print(f"SNR = {snr_linear:.2e} = {snr_db:.2f} dB")
    
    # 容量计算
    capacity_bps = bn * np.log2(1 + snr_linear)
    capacity_mbps = capacity_bps / 1e6
    print(f"容量 = bn × log2(1 + SNR) = {bn/1e6:.1f}M × log2(1 + {snr_linear:.2e})")
    print(f"容量 = {capacity_mbps:.2f} Mbps")
    
    print("\n📊 3. 不同距离下的容量测试")
    print("-" * 50)
    
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    test_distances = [10, 50, 100, 200, 500, 800, 1000, 1200, 1500]
    
    print("距离(m) | 平均容量(Mbps) | 最小容量(Mbps) | 最大容量(Mbps) | 标准差(Mbps)")
    print("-" * 80)
    
    for distance in test_distances:
        capacities = []
        
        # 每个距离测试50次
        for _ in range(50):
            capacity = env._calculate_shannon_capacity(0, 0, distance, 0)
            if capacity > 0:
                capacities.append(capacity / 1e6)  # 转换为Mbps
        
        if capacities:
            avg_capacity = np.mean(capacities)
            min_capacity = np.min(capacities)
            max_capacity = np.max(capacities)
            std_capacity = np.std(capacities)
            
            print(f"{distance:6d}  |     {avg_capacity:7.2f}     |     {min_capacity:7.2f}     |     {max_capacity:7.2f}     |    {std_capacity:6.2f}")
        else:
            print(f"{distance:6d}  |      无通信      |      无通信      |      无通信      |     无通信")

def test_parameter_sensitivity():
    """测试参数敏感性"""
    print("\n" + "="*80)
    print("参数敏感性分析")
    print("="*80)
    
    env = EdgeComputingEnvironment(num_devices=1, num_servers=1, random_seed=42)
    
    print("\n🔍 1. 带宽影响测试")
    print("-" * 50)
    
    # 测试不同带宽下的容量（固定距离100m）
    bandwidths = [5e6, 10e6, 20e6, 30e6, 40e6]  # 5-40 MHz
    distance_fixed = 100
    
    print("带宽(MHz) | 理论容量(Mbps) | 实际平均容量(Mbps)")
    print("-" * 50)
    
    for bw in bandwidths:
        # 理论计算（无衰落）
        hn = config.physical.channel_gain_constant / (distance_fixed ** config.physical.path_loss_exponent)
        N0_watts_hz = 10 ** ((config.network.noise_psd_dbm_hz - 30) / 10)
        snr_linear = (config.network.tx_power_watts * hn) / (bw * N0_watts_hz)
        theoretical_capacity = bw * np.log2(1 + snr_linear) / 1e6
        
        # 实际测试（有衰落和随机性）
        # 临时修改带宽范围进行测试
        original_min = config.network.bandwidth_min
        original_max = config.network.bandwidth_max
        config.network.bandwidth_min = bw
        config.network.bandwidth_max = bw
        
        capacities = []
        for _ in range(20):
            capacity = env._calculate_shannon_capacity(0, 0, distance_fixed, 0)
            if capacity > 0:
                capacities.append(capacity / 1e6)
        
        # 恢复原始设置
        config.network.bandwidth_min = original_min
        config.network.bandwidth_max = original_max
        
        if capacities:
            avg_capacity = np.mean(capacities)
            print(f"  {bw/1e6:5.0f}   |      {theoretical_capacity:7.2f}      |        {avg_capacity:7.2f}")
        else:
            print(f"  {bw/1e6:5.0f}   |      {theoretical_capacity:7.2f}      |         无通信")
    
    print("\n🔍 2. 路径损耗指数影响测试")
    print("-" * 50)
    
    # 测试不同路径损耗指数
    path_loss_exponents = [1.5, 2.0, 2.5, 3.0, 3.5, 4.0]
    distance_fixed = 200
    
    print("路径损耗指数 | 信道增益 | 平均容量(Mbps)")
    print("-" * 45)
    
    for exponent in path_loss_exponents:
        # 临时修改路径损耗指数
        original_exponent = config.physical.path_loss_exponent
        config.physical.path_loss_exponent = exponent
        
        # 计算信道增益
        hn = config.physical.channel_gain_constant / (distance_fixed ** exponent)
        
        capacities = []
        for _ in range(20):
            capacity = env._calculate_shannon_capacity(0, 0, distance_fixed, 0)
            if capacity > 0:
                capacities.append(capacity / 1e6)
        
        # 恢复原始设置
        config.physical.path_loss_exponent = original_exponent
        
        if capacities:
            avg_capacity = np.mean(capacities)
            print(f"     {exponent:3.1f}      |  {hn:.2e}  |     {avg_capacity:7.2f}")
        else:
            print(f"     {exponent:3.1f}      |  {hn:.2e}  |      无通信")

def test_rate_range_compliance():
    """测试速率范围符合性"""
    print("\n" + "="*80)
    print("速率范围符合性测试")
    print("="*80)
    
    env = EdgeComputingEnvironment(num_devices=2, num_servers=2, random_seed=42)
    
    # 收集大量样本
    all_rates = []
    
    print("正在收集速率样本...")
    for _ in range(1000):
        env._update_network_conditions()
        
        for condition in env.network_conditions:
            for rate in condition['server_rates']:
                if rate > 0:
                    all_rates.append(rate / 1e6)  # 转换为Mbps
    
    if all_rates:
        print(f"\n📊 速率统计 (样本数: {len(all_rates)}):")
        print("-" * 50)
        print(f"最小速率: {np.min(all_rates):.2f} Mbps")
        print(f"最大速率: {np.max(all_rates):.2f} Mbps")
        print(f"平均速率: {np.mean(all_rates):.2f} Mbps")
        print(f"中位数: {np.median(all_rates):.2f} Mbps")
        print(f"标准差: {np.std(all_rates):.2f} Mbps")
        
        print(f"\n🎯 目标范围符合性:")
        print("-" * 50)
        print(f"目标范围: [{config.network.rate_min/1e6:.0f}, {config.network.rate_max/1e6:.0f}] Mbps")
        
        in_range_count = sum(1 for rate in all_rates 
                           if config.network.rate_min/1e6 <= rate <= config.network.rate_max/1e6)
        compliance_rate = in_range_count / len(all_rates) * 100
        
        print(f"范围内样本: {in_range_count}/{len(all_rates)} ({compliance_rate:.1f}%)")
        
        if compliance_rate > 95:
            print("✅ 速率范围符合性优秀")
        elif compliance_rate > 80:
            print("✅ 速率范围符合性良好")
        else:
            print("⚠️ 速率范围符合性需要改进")
    else:
        print("❌ 未收集到有效速率样本")

def main():
    """主测试函数"""
    print("正确香农公式实现测试")
    print("=" * 80)
    
    test_shannon_formula()
    test_parameter_sensitivity()
    test_rate_range_compliance()
    
    print("\n" + "="*80)
    print("测试完成")
    print("="*80)
    print("香农公式: Rn = bn × log2(1 + (pn × hn) / (bn × N0))")
    print("参数: pn=1W, N0=-174dBm/Hz, 目标范围=[52,108]Mbps")

if __name__ == "__main__":
    main()
