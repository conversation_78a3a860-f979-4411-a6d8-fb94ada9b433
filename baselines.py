import numpy as np
import networkx as nx
from typing import List, Dict, Any

from config import config
from models import LocalDevice, EdgeInferenceTask, EdgeServer
from performance import DelayModel, EnergyModel, AdaptiveWeightMechanism
from resnet_layer_info import calculate_flops_up_to_layer, get_output_size_at_layer

class MaxFlowMinCutBaseline:
    """
    A baseline algorithm that uses max-flow min-cut to determine the optimal
    partition point for each task to minimize a weighted objective function of
    delay and energy.

    This models the problem as finding a minimum cut in a graph where one side
    represents local execution and the other represents edge execution. The weights
    of the edges correspond to the costs (a combination of delay and energy) of
    making a particular execution choice.
    """

    def __init__(self):
        self.delay_model = DelayModel()
        self.energy_model = EnergyModel()
        self.weight_mechanism = AdaptiveWeightMechanism()
        self.config = config

    def get_actions(self, devices: List[LocalDevice], tasks: List[EdgeInferenceTask],
                    servers: List[EdgeServer], network_conditions: List[Dict]) -> List[Dict[str, Any]]:
        """Get actions for all devices by solving a min-cut problem for each."""
        actions = []
        for i in range(len(devices)):
            action = self._get_single_action(
                devices[i], tasks[i], servers, network_conditions[i]
            )
            actions.append(action)
        return actions

    def _get_single_action(self, device: LocalDevice, task: EdgeInferenceTask,
                           servers: List[EdgeServer], network_condition: Dict) -> Dict[str, Any]:
        """Determines the optimal action for a single device-task pair using min-cut."""
        # --- Enhanced Server Selection with Distance and Queue Consideration ---
        device_x, device_y = device.get_position()

        # Calculate server scores based on load, distance, and queue status
        server_scores = []
        for server in servers:
            server_x, server_y = server.get_position()
            distance = device.calculate_distance_to(server_x, server_y)

            # Normalize factors
            load_factor = server.get_total_load() / server.max_queue_size  # 0-1
            distance_factor = min(distance / config.physical.max_distance, 1.0)  # 0-1
            queue_factor = server.get_queue_length() / server.max_queue_size  # 0-1

            # Combined score (lower is better)
            score = 0.4 * load_factor + 0.3 * distance_factor + 0.3 * queue_factor
            server_scores.append(score)

        server_id = np.argmin(server_scores) if server_scores else 0
        target_server = servers[server_id]

        # Use a high frequency as a heuristic to minimize the delay component of the cost
        frequency = device.f_max

        # Get all valid partition points (including 0 for full local and early_exit_point+1 for full offload)
        partition_points = [0] + [p for p in task.valid_partition_points if p <= task.early_exit_point]
        partition_points = sorted(list(set(partition_points)))
        partition_points.append(task.early_exit_point + 1)  # Add full offload option

        # Calculate cost for each partition point
        alpha = device.get_adaptive_weight()
        min_cost = float('inf')
        best_partition = 0

        for partition_point in partition_points:
            total_cost = self._calculate_partition_cost(
                device, task, target_server, network_condition,
                partition_point, frequency, alpha, server_id
            )

            if total_cost < min_cost:
                min_cost = total_cost
                best_partition = partition_point

        return {'partition_point': best_partition, 'server_id': server_id, 'frequency': frequency}

    def _calculate_partition_cost(self, device: LocalDevice, task: EdgeInferenceTask,
                                  server: EdgeServer, network_condition: Dict,
                                  partition_point: int, frequency: float, alpha: float,
                                  server_id: int) -> float:
        """Calculate the total cost for a given partition point with distance-based rates and queuing."""

        # Full local execution
        if partition_point == 0:
            total_flops = calculate_flops_up_to_layer(task.early_exit_point)
            local_delay = self.delay_model.calculate_local_delay(total_flops, frequency, device.g_device)
            local_energy = self.energy_model.calculate_computation_energy(frequency, local_delay, device.kappa)
            return alpha * local_delay + (1 - alpha) * local_energy

        # Full offload
        elif partition_point > task.early_exit_point:
            # Get distance-based transmission rate
            transmission_rate = network_condition['server_rates'][server_id]

            # Communication delay and energy for sending input data
            input_data_size = get_output_size_at_layer(0)  # Input image size
            comm_delay = self.delay_model.calculate_communication_delay(input_data_size, transmission_rate)
            comm_energy = self.energy_model.calculate_communication_energy(input_data_size, transmission_rate, device.p_tx)

            # Edge processing delay (no energy cost for edge server)
            total_flops = calculate_flops_up_to_layer(task.early_exit_point)
            edge_delay = self.delay_model.calculate_edge_delay(total_flops, server.f_server, server.g_server)

            # Add queuing delay
            queuing_delay = server.calculate_queuing_delay()

            total_delay = comm_delay + edge_delay + queuing_delay
            return alpha * total_delay + (1 - alpha) * comm_energy

        # Partial offload (hybrid execution)
        else:
            # Get distance-based transmission rate
            transmission_rate = network_condition['server_rates'][server_id]

            # Local computation cost
            local_flops = calculate_flops_up_to_layer(partition_point)
            local_delay = self.delay_model.calculate_local_delay(local_flops, frequency, device.g_device)
            local_energy = self.energy_model.calculate_computation_energy(frequency, local_delay, device.kappa)

            # Communication cost
            data_size = get_output_size_at_layer(partition_point)
            comm_delay = self.delay_model.calculate_communication_delay(data_size, transmission_rate)
            comm_energy = self.energy_model.calculate_communication_energy(data_size, transmission_rate, device.p_tx)

            # Edge computation cost
            edge_flops = calculate_flops_up_to_layer(task.early_exit_point) - local_flops
            edge_delay = self.delay_model.calculate_edge_delay(edge_flops, server.f_server, server.g_server)

            # Add queuing delay
            queuing_delay = server.calculate_queuing_delay()

            total_delay = local_delay + comm_delay + edge_delay + queuing_delay
            total_energy = local_energy + comm_energy

            return alpha * total_delay + (1 - alpha) * total_energy


class FullLocalBaseline:
    """Baseline that always executes tasks locally at the lowest frequency."""
    def get_actions(self, devices: List[LocalDevice], **kwargs) -> List[Dict[str, Any]]:
        return [{'partition_point': 0, 'server_id': 0, 'frequency': dev.f_min} for dev in devices]


class FullOffloadBaseline:
    """Baseline that always offloads tasks to the best server considering distance and load."""
    def get_actions(self, devices: List[LocalDevice], tasks: List[EdgeInferenceTask],
                    servers: List[EdgeServer], **kwargs) -> List[Dict[str, Any]]:
        actions = []
        for i in range(len(devices)):
            device = devices[i]
            device_x, device_y = device.get_position()

            # Calculate server scores based on load, distance, and queue status
            server_scores = []
            for server in servers:
                server_x, server_y = server.get_position()
                distance = device.calculate_distance_to(server_x, server_y)

                # Normalize factors
                load_factor = server.get_total_load() / server.max_queue_size  # 0-1
                distance_factor = min(distance / config.physical.max_distance, 1.0)  # 0-1
                queue_factor = server.get_queue_length() / server.max_queue_size  # 0-1

                # Combined score (lower is better)
                score = 0.4 * load_factor + 0.3 * distance_factor + 0.3 * queue_factor
                server_scores.append(score)

            server_id = np.argmin(server_scores) if server_scores else 0
            actions.append({
                'partition_point': tasks[i].early_exit_point + 1,
                'server_id': server_id,
                'frequency': devices[i].f_max
            })
        return actions
